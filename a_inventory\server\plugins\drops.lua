local drops, openDrops, clientDrops = {}, {}, {}
local drop = Inventory:Create('drop')
local allowedInventories = {
  "player",
  "drop"
}

local function getNearbyDrop(coords)
  for k, v in next, drops do
    local dist = #(v.pos - coords)

    if dist < Config.Drops.radius then
      return k
    end
  end

  return nil
end

---@param coords table
local function createDrop(coords)
  local nearbyDrop = getNearbyDrop(coords)

  if nearbyDrop then
    return nearbyDrop
  end

  local dropId = GenerateUUID()

  while drops[dropId] do
    dropId = GenerateUUID()

    Citizen.Wait(0)
  end

  drops[dropId] = {
    pos = coords,
    items = {},
    lastUpdated = os.time()
  }

  clientDrops[dropId] = {
    pos = coords
  }

  DebugLog("Created drop: " .. dropId)

  TriggerClientEvent("a_inventory:refreshDrops", -1, clientDrops)

  return dropId
end

local function deleteDrop(dropId)
  drops[dropId] = nil
  clientDrops[dropId] = nil

  for _, v in next, openDrops[dropId] do
    TriggerClientEvent("a_inventory:closeDrop", v)
  end

  openDrops[dropId] = nil

  TriggerClientEvent("a_inventory:refreshDrops", -1, clientDrops)
end

---@param dropId string
---@param itemData table
---@param amount number
---@param components? table
---@param tint? number
---@param ammo? number
---@return boolean, string
local function AddToDrop(dropId, itemData, amount, components, tint, ammo)
  DebugLog("AddToDrop", dropId, itemData, amount, components, tint, ammo)
  local dropItems = drops[dropId]?.items or false

  if not dropItems then
    return false, GetLocale("plugins.drops.doesnt_exist")
  end

  local totalWeight = Inventory:GetWeight(dropItems)

  if totalWeight + ((itemData?.weight or 0) * amount) > Config.Drops.maxWeight then
    return false, GetLocale("plugins.drops.max_weight")
  end

  for i = 1, #dropItems do
    local item = dropItems[i]

    if item.name == itemData.name and itemData.type ~= "weapon" then
      item.count += amount

      return true, ""
    elseif item.name == itemData.name and itemData.type == "weapon" then
      return false, GetLocale("plugins.drops.already_has_weapon")
    end
  end

  dropItems[#dropItems + 1] = {
    name = itemData.name,
    count = amount,
    weight = itemData.weight,
    type = itemData.type,
    label = itemData.label,
    usable = itemData.usable,
    canRemove = itemData.canRemove,
  }

  if components then
    dropItems[#dropItems].components = components
  end

  if tint then
    dropItems[#dropItems].tint = tint
  end

  if ammo then
    dropItems[#dropItems].ammo = ammo
  end

  drops[dropId].lastUpdated = os.time()

  IssueLog("info", "Added " .. amount .. "x " .. itemData.label .. " to drop " .. dropId, "drop_add")
  return true, ""
end

---@param dropId string
---@param itemData table
---@param amount number
---@return boolean, string
local function RemoveFromDrop(dropId, itemData, amount)
  local dropItems = drops[dropId]?.items or false

  if not dropItems then
    return false, GetLocale("plugins.drops.doesnt_exist")
  end

  for i = 1, #dropItems do
    local item = dropItems[i]

    if item.name == itemData.name then
      if item.count < amount then
        return false, GetLocale("plugins.drops.not_enough")
      end

      item.count -= amount

      if item.count <= 0 then
        table.remove(dropItems, i)
      end

      drops[dropId].lastUpdated = os.time()

      if #dropItems == 0 then
        deleteDrop(dropId)
      end

      IssueLog("info", "Removed " .. amount .. "x " .. item.label .. " from drop " .. dropId, "drop_remove")
      return true, ""
    end
  end

  return false, GetLocale("plugins.drops.doesnt_contain")
end

local function broadcastDropUpdate(dropId)
  local players = openDrops[dropId] or {}

  DebugLog("Broadcasting drop update to " .. #players .. " players")

  if not players or #players == 0 then
    return
  end

  for i = 1, #players do
    TriggerClientEvent("a_inventory:updateDrop", players[i], drops[dropId].items)
  end
end

drop:On("getInventory", function(source, cb, id)
  local dropData = drops[id]

  if not dropData then
    return cb({
      state = false,
      error = GetLocale("plugins.drops.doesnt_exist"),
      items = {},
      maxWeight = 0
    })
  end

  local playerCoords = GetPlayerPed(source)
  local dist = #(GetEntityCoords(playerCoords) - dropData.pos)

  if dist > Config.Drops.radius then
    return cb({
      state = false,
      error = GetLocale("plugins.drops.too_far"),
      items = {},
      maxWeight = 0
    })
  end

  if not openDrops[id] then
    openDrops[id] = {
      source
    }
  end

  cb({
    state = true,
    error = "",
    items = dropData.items,
    maxWeight = Config.Drops.maxWeight
  })
end)

drop:On("moveItem", function(source, cb, item, from, to, amount, dropId)
  local dropData = drops[dropId]
  local playerPed = GetPlayerPed(source)
  local playerCoords = GetEntityCoords(playerPed)

  if not table.includes(allowedInventories, from?.type or "") or not table.includes(allowedInventories, to?.type or "") then
    return cb({
      state = false,
      error = GetLocale("plugins.drops.invalid_inventory")
    })
  end

  if not dropData and to.type == "player" then
    return cb({
      state = false,
      error = GetLocale("plugins.drops.doesnt_exist")
    })
  end

  if dropData and from.type == "drop" then
    local isOpen = table.includes(openDrops[dropId] or {}, source)

    if not isOpen then
      return cb({
        state = false,
        error = GetLocale("plugins.drops.not_allowed")
      })
    end
  end

  DebugLog("Moving item from " .. from.type .. " to " .. to.type)
  DebugLog("Item: " .. item.name .. " (" .. item.type .. ")")
  DebugLog("Amount: " .. type(amount) == "number" and amount or "nil")

  if amount == nil or amount < 1 then
    return cb({
      state = false,
      error = GetLocale("plugins.drops.invalid_amount")
    })
  end

  local xPlayer = ESX.GetPlayerFromId(source)

  if not xPlayer then
    return cb({
      state = false,
      error = GetLocale("plugins.drops.error_occured", "E-PDR-001")
    })
  end

  if to.type == "drop" and from.type == "player" then
    if amount < 1 then
      return cb({
        state = false,
        error = GetLocale("plugins.drops.invalid_amount")
      })
    end

    if not dropData then
      local newDropId = createDrop(playerCoords)

      dropData = drops[newDropId]
      dropId = newDropId
    end

    local success, resp = Item:Get(item.name, item.type)

    if not success then
      return cb({
        state = false,
        error = resp
      })
    end

    local dist = #(playerCoords - dropData.pos)

    if dist > Config.Drops.radius then
      return cb({
        state = false,
        error = GetLocale("plugins.drops.too_far_away")
      })
    end

    -- ANALYZE THIS
    local weight = Inventory:GetWeight(dropData.items)

    if weight + (item.weight * amount) > Config.Drops.maxWeight then
      return cb({
        state = false,
        error = GetLocale("plugins.drops.max_weight")
      })
    end

    local itemCache = {}

    if item.type == "money" then
      local account = xPlayer.getAccount(item.name)

      if not account then
        return cb({
          state = false,
          error = GetLocale("plugins.drops.account_doesnt_exist")
        })
      end

      if account.money < amount then
        return cb({
          state = false,
          error = GetLocale("plugins.drops.not_enough_money")
        })
      end

      xPlayer.removeAccountMoney(item.name, amount)
    elseif item.type == "weapon" then
      if not xPlayer.hasWeapon(item.name) then
        return cb({
          state = false,
          error = GetLocale("plugins.drops.dont_have_weapon")
        })
      end

      local loadout = xPlayer.getLoadout(true)
      local tint = xPlayer.getWeaponTint(string.upper(item.name))
      local weaponData = loadout[string.upper(item.name)]

      itemCache = {
        ammo = weaponData?.ammo or 0,
        components = weaponData?.components or {},
        tint = type(tint) ~= "nil" and tint or 0
      }

      xPlayer.removeWeapon(item.name)
    elseif item.type == "item" then
      local inventoryItem = xPlayer.getInventoryItem(item.name)

      if not inventoryItem then
        return cb({
          state = false,
          error = GetLocale("plugins.drops.dont_have_item")
        })
      end

      if inventoryItem.count < amount then
        return cb({
          state = false,
          error = GetLocale("plugins.drops.not_enough_item")
        })
      end

      xPlayer.removeInventoryItem(item.name, amount)
    else
      return cb({
        state = false,
        error = GetLocale("plugins.drops.invalid_type")
      })
    end

    local success, error = AddToDrop(dropId, type(resp) == "table" and resp or {}, amount, itemCache.components,
      itemCache.tint, itemCache.ammo)

    if success then
      broadcastDropUpdate(dropId)
      return cb({
        state = true,
        error = ""
      })
    end

    if item.type == "money" then
      xPlayer.addAccountMoney(item.name, amount)
    elseif item.type == "weapon" then
      xPlayer.addWeapon(item.name, itemCache.ammo)

      for i = 1, #itemCache.components do
        ---@diagnostic disable-next-line: param-type-mismatch
        xPlayer.addWeaponComponent(item.name, itemCache.components[i])
      end

      if itemCache.tint then
        xPlayer.setWeaponTint(item.name, itemCache.tint)
      end
    elseif item.type == "item" then
      xPlayer.addInventoryItem(item.name, amount)
    end

    return cb({
      state = false,
      error = error
    })
  elseif to.type == "player" and from.type == "drop" then
    if item.type == "item" then
      local canCarry = xPlayer.canCarryItem(item.name, amount)

      if not canCarry then
        return cb({
          state = false,
          error = GetLocale("plugins.drops.cant_carry")
        })
      end
    elseif item.type == "weapon" then
      local hasWeapon = xPlayer.hasWeapon(item.name)

      if hasWeapon then
        return cb({
          state = false,
          error = GetLocale("plugins.drops.player_already_has_weapon")
        })
      end
    end

    local success, resp = Item:Get(item.name, item.type)

    if not success then
      return cb({
        state = false,
        error = resp
      })
    end

    local success, error = RemoveFromDrop(dropId, type(resp) == "table" and resp or {}, amount)

    if not success then
      return cb({
        state = false,
        error = error
      })
    end

    if item.type == "money" then
      xPlayer.addAccountMoney(item.name, amount)
    elseif item.type == "weapon" then
      xPlayer.addWeapon(item.name, item.ammo)

      if item.components then
        DebugLog("Adding weapon components")

        for i = 1, #item.components do
          xPlayer.addWeaponComponent(item.name, item.components[i])
        end
      end

      if item.tint then
        xPlayer.setWeaponTint(item.name, item.tint)
      end
    elseif item.type == "item" then
      if not xPlayer.canCarryItem(item.name, amount) then
        return cb({
          state = false,
          error = GetLocale("plugins.drops.cant_carry")
        })
      end

      xPlayer.addInventoryItem(item.name, amount)
    end

    broadcastDropUpdate(dropId)

    return cb({
      state = true,
      error = ""
    })
  end
end)

Citizen.CreateThread(function()
  local currentTime = os.time()

  while true do
    currentTime = os.time()

    for k, v in next, drops do
      local isOpen = openDrops[k]

      if isOpen then
        goto continue
      end

      if currentTime - v.lastUpdated > Config.Drops.lifetime then
        drops[k] = nil
        clientDrops[k] = nil

        TriggerClientEvent("a_inventory:refreshDrops", -1, clientDrops)
      end

      ::continue::
    end

    Citizen.Wait(1000)
  end
end)

RegisterNetEvent("a_inventory:closeDrop", function()
  local source = source

  for k, v in next, openDrops do
    if table.includes(v, source) then
      table.remove(v, table.indexOf(v, source))

      if #v == 0 then
        openDrops[k] = nil
      end

      break
    end
  end
end)