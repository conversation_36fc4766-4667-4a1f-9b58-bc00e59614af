local gloveboxes, openGloveboxes = {}, {}
local glovebox = Inventory:Create("glovebox")
local isInstalled = pcall(MySQL.query.await, "SELECT * FROM gloveboxes")
local allowedInventories = {
  "player",
  "glovebox"
}

if not isInstalled then
  print("Glovebox plugin installation not found. Installing...")

  MySQL.Sync.execute([[
    CREATE TABLE IF NOT EXISTS gloveboxes (
      plate VARCHAR(255) PRIMARY KEY,
      items TEXT
    )
  ]])

  print("Glovebox plugin installation complete. Please restart the resource to apply changes.")
end

local function broadcastGloveboxUpdate(plate)
  local players = openGloveboxes[plate] or {}

  if players then
    for i = 1, #players do
      TriggerClientEvent("a_inventory:updateGlovebox", players[i], gloveboxes[plate].items)
    end
  end
end

---@param plate string
---@param itemData table
---@param amount number
---@param maxSpace number
---@param components? table
---@param tint? number
---@param ammo? number
---@return boolean, string
local function AddToGloveBox(plate, itemData, amount, maxSpace, components, tint, ammo)
  local gloveboxItems = gloveboxes[plate]

  if not gloveboxItems then
    return false, GetLocale("plugins.glovebox.vehicle_doesnt_have_glovebox")
  end

  local items = json.decode(gloveboxItems.items)

  if not items then
    return false, GetLocale("plugins.glovebox.error_occured", "E-EF-1")
  end

  local totalWeight = 0

  for i = 1, #items do
    totalWeight += ((items[i]?.weight or 0) * items[i].count)
  end

  if totalWeight + ((itemData?.weight or 0) * amount) > maxSpace then
    return false, GetLocale("plugins.glovebox.glovebox_too_small")
  end

  for i = 1, #items do
    if items[i].name == itemData.name and itemData.type ~= "weapon" then
      items[i].count += amount
      gloveboxItems.items = json.encode(items)

      MySQL.Async.execute("UPDATE gloveboxes SET items = @items WHERE plate = @plate", {
        ['@items'] = gloveboxItems.items,
        ['@plate'] = plate
      })

      gloveboxes[plate] = gloveboxItems

      return true, ""
    elseif items[i].name == itemData.name and itemData.type == "weapon" then
      return false, GetLocale("plugins.glovebox.only_one_weapon")
    end
  end

  items[#items + 1] = {
    name = itemData.name,
    count = amount,
    weight = itemData.weight,
    type = itemData.type,
    label = itemData.label,
    usable = itemData.usable,
    canRemove = itemData.canRemove,
  }

  if components then
    items[#items].components = components
  end

  if tint then
    items[#items].tint = tint
  end

  if ammo then
    items[#items].ammo = ammo
  end

  gloveboxItems.items = json.encode(items)

  MySQL.Async.execute("UPDATE gloveboxes SET items = @items WHERE plate = @plate", {
    ['@items'] = gloveboxItems.items,
    ['@plate'] = plate
  })

  gloveboxes[plate] = gloveboxItems

  IssueLog("info", ("Added %s x%s to %s's glovebox"):format(itemData.label, amount, plate), "glovebox")
  return true, ""
end

---@param plate string
---@param itemData table
---@param amount number
---@return boolean, string
local function RemoveFromGlovebox(plate, itemData, amount)
  local gloveboxItems = gloveboxes[plate]

  if not gloveboxItems then
    return false, GetLocale("plugins.glovebox.vehicle_doesnt_have_glovebox")
  end

  local items = json.decode(gloveboxItems.items)

  if not items then
    return false, GetLocale("plugins.glovebox.error_occured", "E-EF-2")
  end

  for i = 1, #items do
    if items[i].name == itemData.name then
      if items[i].count < amount then
        return false, GetLocale("plugins.glovebox.not_enough_item")
      end

      items[i].count -= amount

      if items[i].count == 0 then
        table.remove(items, i)
      end

      gloveboxItems.items = json.encode(items)

      MySQL.Async.execute("UPDATE gloveboxes SET items = @items WHERE plate = @plate", {
        ['@items'] = gloveboxItems.items,
        ['@plate'] = plate
      })

      gloveboxes[plate] = gloveboxItems

      IssueLog("info", ("Removed %s x%s from %s's glovebox"):format(itemData.label, amount, plate), "glovebox")
      return true, ""
    end
  end

  return false, GetLocale("plugins.glovebox.item_doesnt_exist_in_glovebox")
end

glovebox:On("getInventory", function(source, cb, plate, vehicleClass, vehicleModel)
  local gloveboxItems = MySQL.query.await("SELECT * FROM gloveboxes WHERE plate = @plate", {
    ['@plate'] = plate
  })?[1]

  local maxWeight = Config.BlacklistedVehicleTypesGB[vehicleClass] and nil or
      Config.GloveModels[plate] or
      Config.GloveModels[string.lower(vehicleModel)] or
      Config.GloveWeights[vehicleClass] or Config.GloveboxWeight

  if maxWeight == nil then
    return cb({
      state = false,
      error = GetLocale("plugins.glovebox.vehicle_doesnt_have_glovebox"),
      items = {},
      maxWeight = 0
    })
  end

  if not gloveboxItems then
    local doesExist = MySQL.query.await("SELECT * FROM owned_vehicles WHERE plate = @plate", {
      ['@plate'] = plate
    })?[1]

    if not doesExist then
      return cb({
        state = false,
        error = GetLocale("plugins.glovebox.vehicle_doesnt_exist"),
        items = {},
        maxWeight = 0
      })
    end

    MySQL.Async.execute("INSERT INTO gloveboxes (plate, items) VALUES (@plate, @items)", {
      ['@plate'] = plate,
      ['@items'] = json.encode({})
    })

    gloveboxItems = {
      items = json.encode({}),
    }
  end

  gloveboxes[plate] = gloveboxItems

  cb({
    state = true,
    error = "",
    items = json.decode(gloveboxItems?.items or {}),
    maxWeight = maxWeight
  })

  if not openGloveboxes[plate] then
    openGloveboxes[plate] = {}
  end

  openGloveboxes[plate][#openGloveboxes[plate] + 1] = source
end)

glovebox:On("moveItem", function(source, cb, item, from, to, amount, plate, vehicleType, vehicleModel)
  if not plate then
    return cb({
      state = false,
      error = GetLocale("plugins.glovebox.vehicle_doesnt_exist"),
    })
  end

  if type(amount) == "number" then
    amount = math.floor(amount)
  end

  if not table.includes(allowedInventories, from?.type or "") or not table.includes(allowedInventories, to?.type or "") then
    return cb({
      state = false,
      error = GetLocale("plugins.glovebox.invalid_inventory"),
    })
  end

  local maxWeight = Config.BlacklistedVehicleTypesGB[vehicleType] and nil or
      Config.GloveModels[plate] or
      Config.GloveModels[string.lower(vehicleModel)] or
      Config.GloveWeights[vehicleType] or Config.GloveboxWeight

  local gloveboxItems = gloveboxes[plate]

  if not gloveboxItems then
    return cb({
      state = false,
      error = GetLocale("plugins.glovebox.vehicle_doesnt_have_glovebox"),
    })
  end

  local isOpen = table.includes(openGloveboxes[plate], source)

  if not isOpen then
    return cb({
      state = false,
      error = GetLocale("plugins.glovebox.not_open"),
    })
  end

  local xPlayer = ESX.GetPlayerFromId(source)

  if not xPlayer then
    return cb({
      state = false,
      error = GetLocale("plugins.glovebox.error_occured", "E-1"),
    })
  end

  if to.type == "glovebox" then
    local items = json.decode(gloveboxItems.items)

    if not items then
      return cb({
        state = false,
        error = GetLocale("plugins.glovebox.error_occured", "E-2"),
      })
    end
  elseif to.type == "player" then
    if item.type == "item" then
      local canCarry = xPlayer.canCarryItem(item.name, amount)

      if not canCarry then
        return cb({
          state = false,
          error = GetLocale("plugins.glovebox.cannot_carry_item"),
        })
      end
    end
  end

  local success, resp = Item:Get(item.name, item.type)

  if not success then
    return cb({
      state = false,
      error = resp,
    })
  end

  local itemCache = {}

  if from.type == "player" then
    if item.type == "money" then
      local account = xPlayer.getAccount(item.name)

      if not account then
        return cb({
          state = false,
          error = GetLocale("item_validation.account_does_not_exist"),
        })
      end

      if account.money < amount then
        return cb({
          state = false,
          error = GetLocale("plugins.glovebox.not_enough_money"),
        })
      end

      xPlayer.removeAccountMoney(item.name, amount)
    elseif item.type == "weapon" then
      if not xPlayer.hasWeapon(item.name) then
        return cb({
          state = false,
          error = GetLocale("plugins.glovebox.dont_have_weapon"),
        })
      end

      local loadout = xPlayer.getLoadout(true)
      local tint = xPlayer.getWeaponTint(string.upper(item.name))
      local weaponData = loadout[string.upper(item.name)]

      itemCache = {
        ammo = weaponData.ammo,
        components = weaponData.components,
        tint = tint,
      }

      xPlayer.removeWeapon(item.name)
    elseif item.type == "item" then
      local inventoryItem = xPlayer.getInventoryItem(item.name)

      if not inventoryItem then
        return cb({
          state = false,
          error = GetLocale("item_validation.item_does_not_exist"),
        })
      end

      if inventoryItem.count < amount then
        return cb({
          state = false,
          error = GetLocale("plugins.glovebox.not_enough_item"),
        })
      end

      xPlayer.removeInventoryItem(item.name, amount)
    end

    local success, error = AddToGloveBox(plate, type(resp) == "table" and resp or {}, amount, maxWeight,
      itemCache.components, itemCache.tint, itemCache.ammo)

    if not success then
      if item.type == "money" then
        xPlayer.addAccountMoney(item.name, amount)
      elseif item.type == "weapon" then
        xPlayer.addWeapon(item.name, itemCache.ammo)

        if itemCache.components then
          for i = 1, #itemCache.components do
            ---@diagnostic disable-next-line: param-type-mismatch
            xPlayer.addWeaponComponent(item.name, itemCache.components[i])
          end
        end

        if itemCache.tint then
          xPlayer.setWeaponTint(item.name, itemCache.tint)
        end
      elseif item.type == "item" then
        xPlayer.addInventoryItem(item.name, amount)
      end

      return cb({
        state = false,
        error = error,
      })
    end

    cb({
      state = true,
      error = "",
    })

    broadcastGloveboxUpdate(plate)
  elseif from.type == "glovebox" then
    local success, error = RemoveFromGlovebox(plate, resp, amount)

    if not success then
      return cb({
        state = false,
        error = error,
      })
    end

    if to.type == "player" then
      if item.type == "money" then
        xPlayer.addAccountMoney(item.name, amount)
      elseif item.type == "weapon" then
        xPlayer.addWeapon(item.name, item.ammo)

        if item.components then
          for i = 1, #item.components do
            xPlayer.addWeaponComponent(item.name, item.components[i])
          end
        end

        if item.tint then
          xPlayer.setWeaponTint(item.name, item.tint)
        end
      elseif item.type == "item" then
        if not xPlayer.canCarryItem(item.name, amount) then
          return cb({
            state = false,
            error = GetLocale("plugins.glovebox.cannot_carry_item"),
          })
        end
        
        xPlayer.addInventoryItem(item.name, amount)
      end

      cb({
        state = true,
        error = "",
      })

      broadcastGloveboxUpdate(plate)
    elseif to.type == "glovebox" then
      local success, error = AddToGloveBox(plate, resp, amount, maxWeight)

      if not success then
        return cb({
          state = false,
          error = error,
        })
      end

      cb({
        state = true,
        error = "",
      })

      broadcastGloveboxUpdate(plate)
      -- tweaky ig
    end
  end
end)

RegisterNetEvent("a_inventory:closeGlovebox", function(plate)
  local source = source

  if not plate then
    return
  end

  local players = openGloveboxes[plate]

  if not players then
    return
  end

  for i = 1, #players do
    if players[i] == source then
      table.remove(players, i)

      DebugLog(("Removed Player %s from %s's broadcast list"):format(source, plate))
      break
    end
  end
end)
