local isOpen = false
local drops = {}
local drop = Inventory:Create('drop', "Drop")

RegisterNetEvent("a_inventory:refreshDrops", function(newDrops)
  for _, v in next, drops do
    ESX.Game.DeleteObject(v.objectId)
  end

  drops = newDrops

  for _, v in next, drops do
    ESX.Game.SpawnObject(Config.Drops.prop.model, v.pos, function(obj)
      v.objectId = obj

      SetEntityAsMissionEntity(obj, true, true)
      FreezeEntityPosition(obj, true)
      PlaceObjectOnGroundProperly(obj)
    end, false)
  end
end)

local function openDrop(dropId)
  local doesExist = drops[dropId]

  if not doesExist then
    return ClientNotify("info", "Drops", GetLocale("plugins.drops.doesnt_exist"))
  end

  local items, maxWeight, state, error = Inventory:Get("drop", dropId)

  if not state then
    return ClientNotify("info", "Drops", error)
  end

  drop:Open(items, maxWeight, nil)
  isOpen = true
end

Citizen.CreateThread(function()
  local sleep = 1000
  local isFocused = false

  while true do
    sleep = 1000
    isFocused = IsNuiFocused()

    for k, v in next, drops do
      local dist = #(GetEntityCoords(PED) - v.pos)

      if dist < 5.0 and not isFocused then
        sleep = 0

        DrawMarker(Config.Drops.marker.markerType, v.pos.x, v.pos.y, v.pos.z - 0.4, 0, 0, 0, 0, 0, 0,
          Config.Drops.marker.markerSize.x,
          Config.Drops.marker.markerSize.y,
          Config.Drops.marker.markerSize.z,
          Config.Drops.marker.markerColor.r,
          Config.Drops.marker.markerColor.g,
          Config.Drops.marker.markerColor.b,
          Config.Drops.marker.markerColor.a,
          false, false, 2, nil, nil, false, false)

        if dist < 1.5 then
          HelpUI(GetLocale("plugins.drops.press_to_access"))

          if IsControlJustReleased(0, 38) then
            openDrop(k)
          end
        end
      end
    end

    Citizen.Wait(sleep)
  end
end)

local function GetClosestDrop()
  local playerCoords = GetEntityCoords(PED)
  local maxDist = Config.Drops.radius

  local currentDist = 0
  local nearestDrop = nil

  for k, v in next, drops do
    local dist = #(playerCoords - v.pos)

    if dist < maxDist and (nearestDrop == nil or dist < currentDist) then
      nearestDrop = k
      currentDist = dist
    end

    Citizen.Wait(1)
  end

  return nearestDrop
end

drop:On("moveItem", function(item, from, to, amount)
  local nearDrop = GetClosestDrop() or nil

  DebugLog("Move Item", item, from, to, amount, nearDrop)

  drop:MoveItem(item, from, to, amount, nearDrop)
end)

RegisterNetEvent("a_inventory:updateDrop", function(items)
  drop:Update(items)
end)

RegisterNetEvent("a_inventory:closeDrop", function()
  drop:Close()
  isOpen = false
end)

EventHandler:On("NuiFocus", function(display)
  if not display and isOpen then
    TriggerServerEvent("a_inventory:closeDrop")
    isOpen = false
  end
end)
