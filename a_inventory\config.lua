Config = {} -- do not edit 

Config.useDebug = true -- enables debug mode (prints debug messages to console)

Config.Lang = GetConvar( "txAdmin-locale", "en" )

-- General
Config.DefaultOpenKey = "F2" -- integrated
Config.DropTimeout = 600
Config.CloseOnUse = { "tablet", "simcard", "lockpick", "redpill", "mtablet", "case", "painkiller", "binoculars", "medikit", "bulletproof", "bulletproof2", "joint", "kokain", "lsd", "repairkit", "spray_remover", "waschschlappen", "weaclip", "suppressor", "clip_extended", "clip_box", "clip_drum", "grip", "scope", "flashlight" } -- all items in this array will close the inventory when used
Config.ClickOutsideToClose = true
Config.MiddleClickToUse = true
Config.Blur = false -- integrated

-- Discord (Base is ready, needs to be implemented)
Config.Discord = true
Config.Actions = {
  use = true,
  drop = true,
  give = true,
}

Config.Drops = {
  enabled = true,
  maxWeight = 100,
  lifetime = 600, -- seconds
  radius = 8.0,
  prop = {
    model = "",
  },
  marker = {
    markerType = 20,
    markerSize = {x = 0.6, y = 0.6, z = 0.6},
    markerColor = {r = 146, g = 14, b = 212, a = 255},
  }
}

Config.Give = {
  confirm = "E",
  cancel = "G",
  marker = {
    markerType = 0,
    markerSize = {x = 0.5, y = 0.5, z = 0.5},
    markerColor = {r = 255, g = 0, b = 0, a = 255},
  }
}

-- Glovebox (Completely integrated!)
Config.GloveboxWeight = 30
Config.GloveboxTimeout = 1000
Config.GloveboxSave = true
Config.BlacklistedVehicleTypesGB = {13, 8}

Config.GloveWeights = {
  [0] = 5, --Compact
  [1] = 10, --Sedan
  [2] = 20, --SUV
  [3] = 5, --Coupes
  [4] = 10, --Muscle
  [5] = 5, --Sports Classics
  [6] = 5, --Sports
  [7] = 5, --Super
  [8] = 5, --Motorcycles
  [9] = 50, --Off-road
  [10] = 60, --Industrial
  [11] = 10, --Utility
  [12] = 50, --Vans
  [13] = 0, --Cycles
  [14] = 10, --Boats
  [15] = 5, --Helicopters
  [16] = 0, --Planes
  [17] = 5, --Service
  [18] = 5, --Emergency
  [19] = 5, --Military
  [20] = 5, --Commercial
  [21] = 0 --Trains
}

Config.GloveModels = {
  [joaat('w222mansory')] = 10,
  [joaat('cc_g63keyvany')] = 10,
  [joaat('limgto62')] = 15,
  [joaat('ikx3gls60021')] = 20,
  [joaat('supramk4v2')] = 20,
  [joaat('x5rk')] = 30
}

-- Trunk (Completely integrated!)

Config.TrunkKey = "K" -- key to open the trunk

Config.DefaultWeight = 100 -- default weight for vehicle trunks (if found nowhere)

Config.TrunkWeights = {
    [0] = 5, --Compact
    [1] = 10, --Sedan
    [2] = 20, --SUV
    [3] = 5, --Coupes
    [4] = 10, --Muscle
    [5] = 5, --Sports Classics
    [6] = 5, --Sports
    [7] = 5, --Super
    [8] = 5, --Motorcycles
    [9] = 50, --Off-road
    [10] = 60, --Industrial
    [11] = 10, --Utility
    [12] = 50, --Vans
    [13] = 0, --Cycles
    [14] = 10, --Boats
    [15] = 5, --Helicopters
    [16] = 0, --Planes
    [17] = 5, --Service
    [18] = 5, --Emergency
    [19] = 5, --Military
    [20] = 5, --Commercial
    [21] = 0 --Trains
}

Config.VehicleModel = {
    tug = 1500,
    benson = 2000,
    vetir = 10000,
    guardian = 450,
    mule = 500,
    mule2 = 500,
    gburrito2 = 350,
    w222mansory = 100,
    taco = 500,
    fbi3 = 5000,
    unimog = 1500,
    ['cc g63keyva'] = 750,
    ['limgto62'] = 100,
    ['ikx3gls6002'] = 500,
    ['supramk4v2'] = 200,
    x5rk = 600
}

Config.GloveModels = {
    [joaat('w222mansory')] = 10,
    [joaat('cc_g63keyvany')] = 10,
    [joaat('limgto62')] = 15,
    [joaat('ikx3gls60021')] = 20,
    [joaat('supramk4v2')] = 20,
    [joaat('x5rk')] = 30
}

Config.BlacklistedVehicleTypes = {
    [13] = true
}

Config.UseShare = true -- feature to give items to other players

Config.SecureEvents = {
  onDeath = {
    event = "esx:onPlayerDeath",
    allowedResources = {"es_extended"}
  },
  onSpawn = {
    event = "esx:onPlayerSpawn",
    allowedResources = {"es_extended", "esx_ambulancejob", "esx_multicharacter"}
  },
  onPedChanged = {
    event = "esx:playerPedChanged",
    allowedResources = {"es_extended", "esx_multicharacter"}
  }
}

---@param type string
---@param title string
---@param message string
function ClientNotify(type, title, message)
  ESX.ShowNotification(message)
end

function ServerNotify(source, msg)
  TriggerClientEvent("esx:showNotification", source, msg)
end

function HelpUI(msg)
  ESX.ShowHelpNotification(msg)
end