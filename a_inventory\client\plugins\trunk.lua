local trunk = Inventory:<PERSON>reate('trunk', GetLocale("plugins.trunk.name"))

local currentVehicle = {
  vehicle = nil,
  model = nil,
  plate = nil,
  maxWeight = nil
}

local allowedInventories = {
  "player",
  "trunk"
}

Bind:Create("opentrunk", Config.TrunkKey, "Open Trunk", function()
  if IsPedInAnyVehicle(PED, false) then
    return ClientNotify("info", GetLocale("plugins.trunk.name"), GetLocale("plugins.trunk.cannot_open_in_vehicle"))
  end

  local playerCoords = GetEntityCoords(PED)
  local vehicle, distance = ESX.Game.GetClosestVehicle({
    x = playerCoords.x,
    y = playerCoords.y,
    z = playerCoords.z,
  })

  if distance > 2.5 then
    return
  end

  local plate = GetVehicleNumberPlateText(vehicle)
  local class = GetVehicleClass(vehicle)
  local model = GetDisplayNameFromVehicleModel(GetEntityModel(vehicle))

  local items, maxWeight, state, error = Inventory:Get("trunk", plate, class, model)

  if not state then
    return ClientNotify("info", GetLocale("plugins.trunk.name"), error)
  end

  currentVehicle.vehicle = vehicle
  currentVehicle.model = model
  currentVehicle.plate = plate
  currentVehicle.maxWeight = maxWeight

  trunk:Open(items, maxWeight, plate)
end)

trunk:On("moveItem", function(item, from, to, amount)
  if not currentVehicle.plate or not currentVehicle.maxWeight then
    return ClientNotify("info", GetLocale("plugins.trunk.name"), GetLocale("plugins.trunk.vehicle_not_found"))
  end

  local targetMaxWeight = to.type == "player" and ESX.PlayerData?.maxWeight or currentVehicle.maxWeight

  if from.type == "player" then
    local itemData = Items:GetItem(item.name)

    if not itemData then
      return ClientNotify("info", GetLocale("plugins.trunk.name"), GetLocale("plugins.trunk.item_doesnt_exist"))
    end

    if not trunk:CanAddItem(amount, itemData.weight, targetMaxWeight) then
      return ClientNotify("info", GetLocale("plugins.trunk.name"), GetLocale("plugins.trunk.item_too_heavy"))
    end
  end

  if not table.includes(allowedInventories, from.type) then
    return ClientNotify("info", GetLocale("plugins.trunk.name"), GetLocale("plugins.trunk.item_doesnt_exist"))
  end

  if not table.includes(allowedInventories, to.type) then
    return ClientNotify("info", GetLocale("plugins.trunk.name"), GetLocale("plugins.trunk.item_doesnt_exist"))
  end

  local vehicleClass = GetVehicleClass(currentVehicle.vehicle)

  trunk:MoveItem(item, from, to, amount, currentVehicle.plate, vehicleClass, currentVehicle.model)
end)

RegisterNetEvent("a_inventory:updateTrunk", function(items)
  if not currentVehicle.plate then
    return
  end

  items = json.decode(items)

  trunk:Update(items)
end)

EventHandler:On("NuiFocus", function(display)
  if not display and currentVehicle?.plate then
    TriggerServerEvent("a_inventory:closeTrunk", currentVehicle.plate)

    currentVehicle = {
      vehicle = nil,
      model = nil,
      plate = nil,
      maxWeight = nil
    }
  end
end)
