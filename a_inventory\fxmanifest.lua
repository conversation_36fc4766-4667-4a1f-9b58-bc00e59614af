fx_version 'cerulean'
game 'gta5'
lua54 'yes'

author 'a-services.xyz'
description 'a-service I Best Inventory System with Plugins'
version '1.5.6'

shared_scripts {
  'config.lua',
  'locales.lua',
  
  'shared/lib.lua',
  'shared/debug.lua',
  'shared/table.lua',

  '@es_extended/imports.lua',

  'shared/consts.lua',

  'import.lua'
}

client_scripts {
  'client/vars.lua',
  'client/consts.lua',

  'client/modules/lib.lua',
  'client/modules/callback.lua',
  
  'client/classes/eventhandler.lua',
  'client/classes/event.lua',
  'client/classes/item.lua',
  'client/classes/inventory.lua',

  'client/events/alive.lua',
  'client/events/ped.lua',
  'client/events/item.lua',
  'client/events/money.lua',

  'client/classes/bind.lua',

  'client/modules/giveitem.lua',

  'client/plugins/trunk.lua',
  'client/plugins/drops.lua',
  'client/plugins/glovebox.lua',

  'client/main.lua'
}

server_scripts {
  'sv-config.lua',
  'server/load.lua',

  '@oxmysql/lib/MySQL.lua',

  'server/modules/log.lua',

  'server/classes/item.lua',
  'server/classes/inventory.lua',

  'server/events/actions.lua',

  'server/plugins/trunk.lua',
  'server/plugins/drops.lua',
  'server/plugins/glovebox.lua',

  'server/main.lua'
}

ui_page 'web/dist/index.html'

file 'web/dist/**/*'

escrow_ignore {
  'config.lua',
  'locales.lua',
  'sv-config.lua',
  'client/plugins/**/*',
  'server/plugins/**/*',
}
dependency '/assetpacks'