ESX = exports["es_extended"]:getSharedObject()

-- Lager Koordinaten
local lagerCoords = vector3(229.7393, 223.6549, 105.5485)
local hasLager = false
local lagerData = {}

-- <PERSON><PERSON> und Blip erstellen
Citizen.CreateThread(function()
    -- Blip erstellen
    local blip = AddBlipForCoord(lagerCoords.x, lagerCoords.y, lagerCoords.z)
    SetBlipSprite(blip, 473)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, 0.8)
    SetBlipColour(blip, 2)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Lager System")
    EndTextCommandSetBlipName(blip)

    while true do
        Citizen.Wait(0)
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local distance = #(playerCoords - lagerCoords)

        if distance < 10.0 then
            -- <PERSON>er anzeigen
            DrawMarker(1, lagerCoords.x, lagerCoords.y, lagerCoords.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0, 2.0, 1.0, 0, 255, 0, 100, false, true, 2, false, nil, nil, false)

            if distance < 2.0 then
                -- Text anzeigen
                ESX.ShowHelpNotification('Drücke ~INPUT_FRONTEND_ACCEPT~ (Enter) um das Lager-Menü zu öffnen')

                if IsControlJustReleased(0, 201) then -- Enter Taste
                    OpenLagerMenu()
                end
            end
        end
    end
end)

-- Lager Status beim Spawn prüfen
Citizen.CreateThread(function()
    Citizen.Wait(2000) -- Warten bis ESX geladen ist
    TriggerServerEvent('prada_lager:checkLager')
end)

-- Lager Menu öffnen
function OpenLagerMenu()
    if hasLager then
        -- Lager-Verwaltungsmenü anzeigen
        local elements = {
            {label = '📦 Lager öffnen', value = 'open'},
            {label = '💰 Lager verkaufen', value = 'sell'},
            {label = 'ℹ️ Lager Info', value = 'info'}
        }

        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'lager_menu', {
            title = 'PRADA CITY Lager Verwaltung',
            align = 'top-left',
            elements = elements
        }, function(data, menu)
            if data.current.value == 'open' then
                menu.close()
                OpenLagerInventory()
            elseif data.current.value == 'sell' then
                menu.close()
                OpenSellMenu()
            elseif data.current.value == 'info' then
                ShowLagerInfo()
            end
        end, function(data, menu)
            menu.close()
        end)
    else
        OpenPurchaseMenu()
    end
end

-- Lager Verkaufs-Menü
function OpenSellMenu()
    local sellPrice = math.floor(lagerData.price / 2) -- Halber Preis

    local elements = {
        {label = '✅ Ja, verkaufen für $' .. sellPrice, value = 'confirm'},
        {label = '❌ Abbrechen', value = 'cancel'}
    }

    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'sell_lager', {
        title = 'Lager verkaufen?',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        if data.current.value == 'confirm' then
            menu.close()
            TriggerServerEvent('prada_lager:sellLager')
        elseif data.current.value == 'cancel' then
            menu.close()
            OpenLagerMenu()
        end
    end, function(data, menu)
        menu.close()
        OpenLagerMenu()
    end)
end

-- Kauf Menu
function OpenPurchaseMenu()
    local elements = {
        {label = 'Kleines Lager (400 Plätze) - 50.000€', value = 'small', price = 50000, size = 400},
        {label = 'Mittleres Lager (700 Plätze) - 100.000€', value = 'medium', price = 100000, size = 700},
        {label = 'Großes Lager (1000 Plätze) - 200.000€', value = 'large', price = 200000, size = 1000}
    }

    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'lager_purchase', {
        title = 'Lager kaufen',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        local selectedLager = data.current

        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'confirm_purchase', {
            title = 'Kauf bestätigen',
            align = 'top-left',
            elements = {
                {label = 'Ja, kaufen für ' .. selectedLager.price .. '€', value = 'yes'},
                {label = 'Nein, abbrechen', value = 'no'}
            }
        }, function(data2, menu2)
            if data2.current.value == 'yes' then
                TriggerServerEvent('prada_lager:buyLager', selectedLager.value, selectedLager.price, selectedLager.size)
                menu2.close()
                menu.close()
            else
                menu2.close()
            end
        end, function(data2, menu2)
            menu2.close()
        end)

    end, function(data, menu)
        menu.close()
    end)
end

-- A-Inventory Integration für Lager
local lagerInventory = nil
local currentLager = {
    identifier = nil,
    size = nil,
    items = nil
}

-- Lager Inventar erstellen (basierend auf trunk.lua Pattern)
if GetResourceState('a_inventory') == 'started' then
    lagerInventory = Inventory:Create('lager', 'Prada Lager')

    local allowedInventories = {
        "player",
        "lager"
    }

    -- Event Handler für Item-Bewegungen
    lagerInventory:On("moveItem", function(item, from, to, amount)
        if not currentLager.identifier then
            return ClientNotify("info", "Prada Lager", "Lager nicht gefunden!")
        end

        local targetMaxWeight = to.type == "player" and ESX.PlayerData?.maxWeight or (currentLager.size * 1000)

        if from.type == "player" then
            local itemData = Items:GetItem(item.name)

            if not itemData then
                return ClientNotify("info", "Prada Lager", "Item existiert nicht!")
            end

            if not lagerInventory:CanAddItem(amount, itemData.weight, targetMaxWeight) then
                return ClientNotify("info", "Prada Lager", "Item zu schwer!")
            end
        end

        if not table.includes(allowedInventories, from.type) then
            return ClientNotify("info", "Prada Lager", "Ungültiges Inventar!")
        end

        if not table.includes(allowedInventories, to.type) then
            return ClientNotify("info", "Prada Lager", "Ungültiges Inventar!")
        end

        -- Item bewegen
        lagerInventory:MoveItem(item, from, to, amount, currentLager.identifier)
    end)

    -- Event für Lager-Updates vom Server
    RegisterNetEvent("a_inventory:updateLager", function(items)
        if not currentLager.identifier then
            return
        end

        items = json.decode(items)
        lagerInventory:Update(items)
    end)

    -- Event Handler für Inventar schließen
    EventHandler:On("NuiFocus", function(display)
        if not display and currentLager?.identifier then
            TriggerServerEvent("a_inventory:closeLager", currentLager.identifier)

            currentLager = {
                identifier = nil,
                size = nil,
                items = nil
            }
        end
    end)
end

-- Lager Inventar öffnen
function OpenLagerInventory()
    if not hasLager then
        ESX.ShowNotification('~r~Du hast kein Lager!')
        return
    end

    -- A-Inventory verwenden
    if GetResourceState('a_inventory') == 'started' and lagerInventory then
        local playerData = ESX.GetPlayerData()
        local identifier = playerData.identifier

        -- Lager-Daten vom Server holen
        ESX.TriggerServerCallback('prada_lager:getLagerItems', function(items, maxWeight)
            if items then
                currentLager.identifier = identifier
                currentLager.size = lagerData.size
                currentLager.items = items

                -- A-Inventory Interface öffnen
                lagerInventory:Open(items, maxWeight, identifier)
            else
                ESX.ShowNotification('~r~Fehler beim Laden des Lagers!')
            end
        end)
        return
    end

    -- Fallback zu Standard-Menü
    ESX.ShowNotification('~r~A-Inventory nicht gefunden! Verwende Standard-Menü.')
    OpenLagerInventoryMenu()
end

-- Fallback Standard-Menü
function OpenLagerInventoryMenu()
    local elements = {
        {label = 'Items einlagern', value = 'deposit'},
        {label = 'Items auslagern', value = 'withdraw'},
        {label = 'Lager Info', value = 'info'}
    }

    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'lager_inventory', {
        title = 'Lager Verwaltung',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        if data.current.value == 'deposit' then
            OpenDepositMenu()
        elseif data.current.value == 'withdraw' then
            OpenWithdrawMenu()
        elseif data.current.value == 'info' then
            ShowLagerInfo()
        end
    end, function(data, menu)
        menu.close()
    end)
end

-- Einlagerungs Menu
function OpenDepositMenu()
    ESX.TriggerServerCallback('prada_lager:getPlayerInventory', function(inventory)
        local elements = {}

        for i=1, #inventory, 1 do
            if inventory[i].count > 0 then
                table.insert(elements, {
                    label = inventory[i].label .. ' (' .. inventory[i].count .. ')',
                    value = inventory[i].name,
                    count = inventory[i].count
                })
            end
        end

        if #elements == 0 then
            ESX.ShowNotification('Du hast keine Items zum Einlagern!')
            return
        end

        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'deposit_items', {
            title = 'Items einlagern',
            align = 'top-left',
            elements = elements
        }, function(data, menu)
            local itemName = data.current.value
            local maxCount = data.current.count

            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'deposit_amount', {
                title = 'Anzahl eingeben (Max: ' .. maxCount .. ')'
            }, function(data2, menu2)
                local amount = tonumber(data2.value)

                if amount == nil or amount <= 0 then
                    ESX.ShowNotification('Ungültige Anzahl!')
                    return
                end

                if amount > maxCount then
                    ESX.ShowNotification('Du hast nicht genug Items!')
                    return
                end

                TriggerServerEvent('prada_lager:depositItem', itemName, amount)
                menu2.close()
                menu.close()
            end, function(data2, menu2)
                menu2.close()
            end)
        end, function(data, menu)
            menu.close()
        end)
    end)
end

-- Auslagerungs Menu
function OpenWithdrawMenu()
    ESX.TriggerServerCallback('prada_lager:getLagerInventory', function(lagerItems)
        local elements = {}

        for itemName, count in pairs(lagerItems) do
            ESX.TriggerServerCallback('ESX:GetItemLabel', function(label)
                table.insert(elements, {
                    label = label .. ' (' .. count .. ')',
                    value = itemName,
                    count = count
                })
            end, itemName)
        end

        Citizen.Wait(100) -- Kurz warten damit Labels geladen werden

        if #elements == 0 then
            ESX.ShowNotification('Dein Lager ist leer!')
            return
        end

        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'withdraw_items', {
            title = 'Items auslagern',
            align = 'top-left',
            elements = elements
        }, function(data, menu)
            local itemName = data.current.value
            local maxCount = data.current.count

            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'withdraw_amount', {
                title = 'Anzahl eingeben (Max: ' .. maxCount .. ')'
            }, function(data2, menu2)
                local amount = tonumber(data2.value)

                if amount == nil or amount <= 0 then
                    ESX.ShowNotification('Ungültige Anzahl!')
                    return
                end

                if amount > maxCount then
                    ESX.ShowNotification('Nicht genug Items im Lager!')
                    return
                end

                TriggerServerEvent('prada_lager:withdrawItem', itemName, amount)
                menu2.close()
                menu.close()
            end, function(data2, menu2)
                menu2.close()
            end)
        end, function(data, menu)
            menu.close()
        end)
    end)
end

-- Lager Info anzeigen
function ShowLagerInfo()
    ESX.TriggerServerCallback('prada_lager:getLagerInfo', function(info)
        local usedSlots = 0
        for itemName, count in pairs(info.items) do
            usedSlots = usedSlots + count
        end

        local elements = {
            {label = 'Lager Typ: ' .. info.type, value = nil},
            {label = 'Maximale Plätze: ' .. info.size, value = nil},
            {label = 'Benutzte Plätze: ' .. usedSlots, value = nil},
            {label = 'Freie Plätze: ' .. (info.size - usedSlots), value = nil}
        }

        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'lager_info', {
            title = 'Lager Information',
            align = 'top-left',
            elements = elements
        }, function(data, menu)
            -- Nichts tun
        end, function(data, menu)
            menu.close()
        end)
    end)
end

-- Event Handler
RegisterNetEvent('prada_lager:setLagerStatus')
AddEventHandler('prada_lager:setLagerStatus', function(status, data)
    hasLager = status
    if data then
        lagerData = data
        -- Stash für Inventar-Systeme registrieren
        TriggerServerEvent('prada_lager:registerStash')
    end
end)

RegisterNetEvent('prada_lager:showNotification')
AddEventHandler('prada_lager:showNotification', function(message)
    ESX.ShowNotification(message)
end)

-- Chat-Befehle für Lager-System
RegisterCommand('lager', function(source, args, rawCommand)
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local distance = #(playerCoords - lagerCoords)

    if distance > 5.0 then
        ESX.ShowNotification('~r~Du bist zu weit vom Lager entfernt!')
        return
    end

    OpenLagerMenu()
end, false)

RegisterCommand('lagerkaufen', function(source, args, rawCommand)
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local distance = #(playerCoords - lagerCoords)

    if distance > 5.0 then
        ESX.ShowNotification('~r~Du bist zu weit vom Lager entfernt!')
        return
    end

    if hasLager then
        ESX.ShowNotification('~r~Du hast bereits ein Lager!')
        return
    end

    OpenPurchaseMenu()
end, false)

RegisterCommand('lagerinfo', function(source, args, rawCommand)
    if not hasLager then
        ESX.ShowNotification('~r~Du hast kein Lager!')
        return
    end

    ShowLagerInfo()
end, false)

-- Hilfe-Befehl
RegisterCommand('lagerhilfe', function(source, args, rawCommand)
    ESX.ShowNotification('~b~Lager-Befehle:~n~~w~/lager - Lager öffnen~n~/lagerkaufen - Lager kaufen~n~/lagerinfo - Lager-Info anzeigen')
end, false)