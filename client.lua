ESX = exports["es_extended"]:getSharedObject()

-- Lager Koordinaten
local lagerCoords = vector3(229.7393, 223.6549, 105.5485)
local hasLager = false
local lagerData = {}

-- <PERSON><PERSON> und Blip erstellen
Citizen.CreateThread(function()
    -- Blip erstellen
    local blip = AddBlipForCoord(lagerCoords.x, lagerCoords.y, lagerCoords.z)
    SetBlipSprite(blip, 473)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, 0.8)
    SetBlipColour(blip, 2)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Lager System")
    EndTextCommandSetBlipName(blip)

    while true do
        Citizen.Wait(0)
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local distance = #(playerCoords - lagerCoords)

        if distance < 10.0 then
            -- <PERSON>er anzeigen
            DrawMarker(1, lagerCoords.x, lagerCoords.y, lagerCoords.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0, 2.0, 1.0, 0, 255, 0, 100, false, true, 2, false, nil, nil, false)

            if distance < 2.0 then
                -- Text anzeigen
                ESX.ShowHelpNotification('Drücke ~INPUT_FRONTEND_ACCEPT~ (Enter) um das Lager-Menü zu öffnen')

                if IsControlJustReleased(0, 201) then -- Enter Taste
                    OpenLagerMenu()
                end
            end
        end
    end
end)

-- Lager Status beim Spawn prüfen
Citizen.CreateThread(function()
    Citizen.Wait(2000) -- Warten bis ESX geladen ist
    TriggerServerEvent('prada_lager:checkLager')
end)

-- Lager Menu öffnen
function OpenLagerMenu()
    if hasLager then
        OpenLagerInventory()
    else
        OpenPurchaseMenu()
    end
end

-- Kauf Menu
function OpenPurchaseMenu()
    local elements = {
        {label = 'Kleines Lager (400 Plätze) - 50.000€', value = 'small', price = 50000, size = 400},
        {label = 'Mittleres Lager (700 Plätze) - 100.000€', value = 'medium', price = 100000, size = 700},
        {label = 'Großes Lager (1000 Plätze) - 200.000€', value = 'large', price = 200000, size = 1000}
    }

    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'lager_purchase', {
        title = 'Lager kaufen',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        local selectedLager = data.current

        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'confirm_purchase', {
            title = 'Kauf bestätigen',
            align = 'top-left',
            elements = {
                {label = 'Ja, kaufen für ' .. selectedLager.price .. '€', value = 'yes'},
                {label = 'Nein, abbrechen', value = 'no'}
            }
        }, function(data2, menu2)
            if data2.current.value == 'yes' then
                TriggerServerEvent('prada_lager:buyLager', selectedLager.value, selectedLager.price, selectedLager.size)
                menu2.close()
                menu.close()
            else
                menu2.close()
            end
        end, function(data2, menu2)
            menu2.close()
        end)

    end, function(data, menu)
        menu.close()
    end)
end

-- Lager Inventar - Öffnet das Inventar-Interface
function OpenLagerInventory()
    local playerData = ESX.GetPlayerData()
    local stashId = 'prada_lager_' .. playerData.identifier

    -- Verschiedene Inventar-Systeme unterstützen

    -- OX-Inventory (Priorität 1)
    if GetResourceState('ox_inventory') == 'started' then
        exports['ox_inventory']:openInventory('stash', stashId)
        return
    end

    -- QS-Inventory (Priorität 2)
    if GetResourceState('qs-inventory') == 'started' then
        exports['qs-inventory']:OpenInventory('stash', stashId, {
            maxweight = lagerData.size * 1000,
            slots = lagerData.size,
            label = 'Prada Lager'
        })
        return
    end

    -- ESX InventoryHUD (Priorität 3)
    if GetResourceState('esx_inventoryhud') == 'started' then
        TriggerEvent('esx_inventoryhud:openInventory', {
            type = "storage",
            identifier = stashId,
            title = "Prada Lager",
            weight = lagerData.size * 1000,
            slots = lagerData.size
        })
        return
    end

    -- Andere mögliche Inventar-Systeme
    if GetResourceState('core_inventory') == 'started' then
        exports['core_inventory']:openInventory('stash', stashId)
        return
    end

    if GetResourceState('mf-inventory') == 'started' then
        exports['mf-inventory']:openInventory('stash', stashId)
        return
    end

    -- Fallback zu Standard-Menü falls kein modernes Inventar-System gefunden wird
    ESX.ShowNotification('~r~Kein kompatibles Inventar-System gefunden! Verwende Standard-Menü.')
    OpenLagerInventoryMenu()
end

-- Fallback Standard-Menü
function OpenLagerInventoryMenu()
    local elements = {
        {label = 'Items einlagern', value = 'deposit'},
        {label = 'Items auslagern', value = 'withdraw'},
        {label = 'Lager Info', value = 'info'}
    }

    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'lager_inventory', {
        title = 'Lager Verwaltung',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        if data.current.value == 'deposit' then
            OpenDepositMenu()
        elseif data.current.value == 'withdraw' then
            OpenWithdrawMenu()
        elseif data.current.value == 'info' then
            ShowLagerInfo()
        end
    end, function(data, menu)
        menu.close()
    end)
end

-- Einlagerungs Menu
function OpenDepositMenu()
    ESX.TriggerServerCallback('prada_lager:getPlayerInventory', function(inventory)
        local elements = {}

        for i=1, #inventory, 1 do
            if inventory[i].count > 0 then
                table.insert(elements, {
                    label = inventory[i].label .. ' (' .. inventory[i].count .. ')',
                    value = inventory[i].name,
                    count = inventory[i].count
                })
            end
        end

        if #elements == 0 then
            ESX.ShowNotification('Du hast keine Items zum Einlagern!')
            return
        end

        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'deposit_items', {
            title = 'Items einlagern',
            align = 'top-left',
            elements = elements
        }, function(data, menu)
            local itemName = data.current.value
            local maxCount = data.current.count

            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'deposit_amount', {
                title = 'Anzahl eingeben (Max: ' .. maxCount .. ')'
            }, function(data2, menu2)
                local amount = tonumber(data2.value)

                if amount == nil or amount <= 0 then
                    ESX.ShowNotification('Ungültige Anzahl!')
                    return
                end

                if amount > maxCount then
                    ESX.ShowNotification('Du hast nicht genug Items!')
                    return
                end

                TriggerServerEvent('prada_lager:depositItem', itemName, amount)
                menu2.close()
                menu.close()
            end, function(data2, menu2)
                menu2.close()
            end)
        end, function(data, menu)
            menu.close()
        end)
    end)
end

-- Auslagerungs Menu
function OpenWithdrawMenu()
    ESX.TriggerServerCallback('prada_lager:getLagerInventory', function(lagerItems)
        local elements = {}

        for itemName, count in pairs(lagerItems) do
            ESX.TriggerServerCallback('ESX:GetItemLabel', function(label)
                table.insert(elements, {
                    label = label .. ' (' .. count .. ')',
                    value = itemName,
                    count = count
                })
            end, itemName)
        end

        Citizen.Wait(100) -- Kurz warten damit Labels geladen werden

        if #elements == 0 then
            ESX.ShowNotification('Dein Lager ist leer!')
            return
        end

        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'withdraw_items', {
            title = 'Items auslagern',
            align = 'top-left',
            elements = elements
        }, function(data, menu)
            local itemName = data.current.value
            local maxCount = data.current.count

            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'withdraw_amount', {
                title = 'Anzahl eingeben (Max: ' .. maxCount .. ')'
            }, function(data2, menu2)
                local amount = tonumber(data2.value)

                if amount == nil or amount <= 0 then
                    ESX.ShowNotification('Ungültige Anzahl!')
                    return
                end

                if amount > maxCount then
                    ESX.ShowNotification('Nicht genug Items im Lager!')
                    return
                end

                TriggerServerEvent('prada_lager:withdrawItem', itemName, amount)
                menu2.close()
                menu.close()
            end, function(data2, menu2)
                menu2.close()
            end)
        end, function(data, menu)
            menu.close()
        end)
    end)
end

-- Lager Info anzeigen
function ShowLagerInfo()
    ESX.TriggerServerCallback('prada_lager:getLagerInfo', function(info)
        local usedSlots = 0
        for itemName, count in pairs(info.items) do
            usedSlots = usedSlots + count
        end

        local elements = {
            {label = 'Lager Typ: ' .. info.type, value = nil},
            {label = 'Maximale Plätze: ' .. info.size, value = nil},
            {label = 'Benutzte Plätze: ' .. usedSlots, value = nil},
            {label = 'Freie Plätze: ' .. (info.size - usedSlots), value = nil}
        }

        ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'lager_info', {
            title = 'Lager Information',
            align = 'top-left',
            elements = elements
        }, function(data, menu)
            -- Nichts tun
        end, function(data, menu)
            menu.close()
        end)
    end)
end

-- Event Handler
RegisterNetEvent('prada_lager:setLagerStatus')
AddEventHandler('prada_lager:setLagerStatus', function(status, data)
    hasLager = status
    if data then
        lagerData = data
        -- Stash für Inventar-Systeme registrieren
        TriggerServerEvent('prada_lager:registerStash')
    end
end)

RegisterNetEvent('prada_lager:showNotification')
AddEventHandler('prada_lager:showNotification', function(message)
    ESX.ShowNotification(message)
end)

-- Chat-Befehle für Lager-System
RegisterCommand('lager', function(source, args, rawCommand)
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local distance = #(playerCoords - lagerCoords)

    if distance > 5.0 then
        ESX.ShowNotification('~r~Du bist zu weit vom Lager entfernt!')
        return
    end

    OpenLagerMenu()
end, false)

RegisterCommand('lagerkaufen', function(source, args, rawCommand)
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local distance = #(playerCoords - lagerCoords)

    if distance > 5.0 then
        ESX.ShowNotification('~r~Du bist zu weit vom Lager entfernt!')
        return
    end

    if hasLager then
        ESX.ShowNotification('~r~Du hast bereits ein Lager!')
        return
    end

    OpenPurchaseMenu()
end, false)

RegisterCommand('lagerinfo', function(source, args, rawCommand)
    if not hasLager then
        ESX.ShowNotification('~r~Du hast kein Lager!')
        return
    end

    ShowLagerInfo()
end, false)

-- Hilfe-Befehl
RegisterCommand('lagerhilfe', function(source, args, rawCommand)
    ESX.ShowNotification('~b~Lager-Befehle:~n~~w~/lager - Lager öffnen~n~/lagerkaufen - Lager kaufen~n~/lagerinfo - Lager-Info anzeigen')
end, false)