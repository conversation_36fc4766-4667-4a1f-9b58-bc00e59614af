LOCALES = {
  en = {
    name = "Inventory",

    giveSubmit = "Press %s to give",
    giveCancel = "Press %s to cancel",
    inventoryLabel = "YOUR BACKPACK",
    noitemsFound = "No items found",
    primaryServerName = "PRADA",
    secondaryServerName = "CITY",
    info = "Info",
    use = "Use",
    give = "Give",
    split = "Split",
    amount = "Amount",
    search = "Search",
    groundLabel = "Ground",
    no_players_nearby = "No players nearby",
    cancelled = "Cancelled",

    item_validation = {
      name_required = "Item name is required",
      type_required = "Item type is required",
      invalid_type = "Invalid item type",
      account_does_not_exist = "Account does not exist",
      item_does_not_exist = "Item does not exist",
      weapon_does_not_exist = "Weapon does not exist",
    },

    actions = {
      used = "used",
      too_far_away = "You are too far away from the player",
      dont_have_item = "You do not have this item",
      not_enough_item = "You do not have enough of this item",
      target_cannot_carry = "The player cannot carry this item",
      gave_item = "You gave %s %s %s",
      received_item = "%s gave you %s %s",
      invalid_amount = "Invalid amount",
      disabled = "This action is disabled",
    },

    plugins = {
      drops = {
        doesnt_exist = "This drop doesn't exist",
        press_to_access = "Press ~INPUT_CONTEXT~ to access the drop",
        max_weight = "This drop is at its maximum capacity",
        already_has_weapon = "This drop already has this weapon",
        player_already_has_weapon = "You already have this weapon",
        not_enough = "This drop doesn't have enough of this item",
        doesnt_contain = "This drop doesn't have this item",
        too_far_away = "You are too far away from the drop",
        invalid_inventory = "Invalid inventory type",
        not_allowed = "You are not allowed to access this drop",
        error_occured = "An rare error occured, please contact support (%s)",
        account_doesnt_exist = "Account doesn't exist",
        not_enough_money = "You don't have enough money",
        dont_have_weapon = "You don't have this weapon",
        dont_have_item = "You don't have this item",
        not_enough_item = "You don't have enough of this item",
        invalid_type = "Invalid item type",
        cannot_carry_item = "You cannot carry this item",
        invalid_amount = "Invalid amount",
      },
      trunk = {
        name = "Trunk",
        cannot_open_in_vehicle = "You can't open the trunk while in a vehicle",
        no_vehicle_nearby = "No vehicle nearby",
        vehicle_not_found = "Vehicle not found",
        item_doesnt_exist = "This item doesn't exist",
        item_too_heavy = "This item is too heavy for the trunk",
        vehicle_doenst_have_trunk = "This vehicle doesn't have a trunk",
        error_occured = "An rare error occured, please contact support (%s)",
        trunk_too_small = "The trunk is too small for this item",
        only_one_weapon = "You can only have on of this weapon in the trunk",
        not_enough_item = "You don't have enough of this item",
        item_doesnt_exist_in_trunk = "This item doesn't exist in the trunk",
        vehicle_doesnt_exist = "This vehicle doesn't exist",
        invalid_inventory = "Invalid inventory type",
        not_open = "The trunk is not open",
        cannot_carry_item = "You cannot carry this item",
        not_enough_money = "You don't have enough money",
        dont_have_weapon = "You don't have this weapon",
      },
      glovebox = {
        name = "Glovebox",
        cannot_open_out_vehicle = "You can't open the glovebox while out of a vehicle",
        vehicle_doesnt_have_glovebox = "This vehicle doesn't have a glovebox",
        vehicle_not_found = "Vehicle not found",
        item_doesnt_exist = "This item doesn't exist",
        item_too_heavy = "This item is too heavy for the glovebox",
        error_occured = "An rare error occured, please contact support (%s)",
        glovebox_too_small = "The glovebox is too small for this item",
        only_one_weapon = "You can only have one of this weapon in the glovebox",
        not_enough_item = "You don't have enough of this item",
        item_doesnt_exist_in_glovebox = "This item doesn't exist in the glovebox",
        vehicle_doesnt_exist = "This vehicle doesn't exist",
        invalid_inventory = "Invalid inventory type",
        not_open = "The glovebox is not open",
        cannot_carry_item = "You cannot carry this item",
        not_enough_money = "You don't have enough money",
        dont_have_weapon = "You don't have this weapon",
      }
    }
  },

  de = {
    name = "Inventar",

    giveSubmit = "Drücke %s zum Geben",
    giveCancel = "Drücke %s zum Abbrechen",
    inventoryLabel = "DEIN RUCKSACK",
    noitemsFound = "Keine Items gefunden",
    primaryServerName = "PRADA",
    secondaryServerName = "CITY",
    info = "Info",
    use = "Benutzen",
    give = "Geben",
    split = "Teilen",
    amount = "Anzahl",
    search = "Suche",
    groundLabel = "Boden",
    no_players_nearby = "Keine Spieler in der Nähe",
    cancelled = "Abgebrochen",

    item_validation = {
      name_required = "Item Name wird benötigt",
      type_required = "Item Typ wird benötigt",
      invalid_type = "Ungültiger Item Typ",
      account_does_not_exist = "Account existiert nicht",
      item_does_not_exist = "Item existiert nicht",
      weapon_does_not_exist = "Waffe existiert nicht",
    },

    actions = {
      used = "benutzt",
      too_far_away = "Du bist zu weit weg vom Spieler",
      dont_have_item = "Du hast dieses Item nicht",
      not_enough_item = "Du hast nicht genug von diesem Item",
      target_cannot_carry = "Der Spieler kann dieses Item nicht tragen",
      gave_item = "Du hast %s %s %s gegeben",
      received_item = "%s hat dir %s %s gegeben",
      invalid_amount = "Ungültige Anzahl",
      disabled = "Diese Funktion ist deaktiviert",
    },

    plugins = {
      drops = {
        doesnt_exist = "Dieser Drop existiert nicht",
        press_to_access = "Drücke ~INPUT_CONTEXT~ um auf den Drop zuzugreifen",
        max_weight = "Dieser Drop hat sein maximales Gewicht erreicht",
        already_has_weapon = "Dieser Drop hat diese Waffe bereits",
        player_already_has_weapon = "Du hast diese Waffe bereits",
        not_enough = "Dieser Drop hat nicht genug von diesem Item",
        doesnt_contain = "Dieser Drop hat dieses Item nicht",
        too_far_away = "Du bist zu weit weg vom Drop",
        invalid_inventory = "Ungültiger Inventar Typ",
        not_allowed = "Du hast keine Berechtigung auf diesen Drop zuzugreifen",
        error_occured = "Ein seltener Fehler ist aufgetreten, bitte kontaktiere den Support (%s)",
        account_doesnt_exist = "Account existiert nicht",
        not_enough_money = "Du hast nicht genug Geld",
        dont_have_weapon = "Du hast diese Waffe nicht",
        dont_have_item = "Du hast dieses Item nicht",
        not_enough_item = "Du hast nicht genug von diesem Item",
        invalid_type = "Ungültiger Item Typ",
        cannot_carry_item = "Du kannst dieses Item nicht tragen",
      },
      trunk = {
        name = "Kofferraum",
        cannot_open_in_vehicle = "Du kannst den Kofferraum nicht öffnen während du in einem Fahrzeug bist",
        no_vehicle_nearby = "Kein Fahrzeug in der Nähe",
        vehicle_not_found = "Fahrzeug nicht gefunden",
        item_doesnt_exist = "Dieses Item existiert nicht",
        item_too_heavy = "Dieses Item ist zu schwer für den Kofferraum",
        vehicle_doenst_have_trunk = "Dieses Fahrzeug hat keinen Kofferraum",
        error_occured = "Ein seltener Fehler ist aufgetreten, bitte kontaktiere den Support (%s)",
        trunk_too_small = "Der Kofferraum ist zu klein für dieses Item",
        only_one_weapon = "Du kannst nur eine dieser Waffen im Kofferraum haben",
        not_enough_item = "Du hast nicht genug von diesem Item",
        item_doesnt_exist_in_trunk = "Dieses Item existiert nicht im Kofferraum",
        vehicle_doesnt_exist = "Dieses Fahrzeug existiert nicht",
        invalid_inventory = "Ungültiger Inventar Typ",
        not_open = "Der Kofferraum ist nicht geöffnet",
        cannot_carry_item = "Du kannst dieses Item nicht tragen",
        not_enough_money = "Du hast nicht genug Geld",
        dont_have_weapon = "Du hast diese Waffe nicht",
      },
      glovebox = {
        name = "Handschuhfach",
        vehicle_doesnt_have_glovebox = "Dieses Fahrzeug hat kein Handschuhfach",
        error_occured = "Ein seltener Fehler ist aufgetreten, bitte kontaktiere den Support (%s)",
        glovebox_too_small = "Das Handschuhfach ist zu klein für dieses Item",
        only_one_weapon = "Du kannst nur eine dieser Waffen im Handschuhfach haben",
        not_enough_item = "Du hast nicht genug von diesem Item",
        item_doesnt_exist_in_glovebox = "Dieses Item existiert nicht im Handschuhfach",
        vehicle_doesnt_exist = "Dieses Fahrzeug existiert nicht",
        invalid_inventory = "Ungültiger Inventar Typ",
        not_open = "Das Handschuhfach ist nicht geöffnet",
        cannot_carry_item = "Du kannst dieses Item nicht tragen",
        not_enough_money = "Du hast nicht genug Geld",
        dont_have_weapon = "Du hast diese Waffe nicht",
      }
    }
  }
}
