# a_inventory

## Exports

# CLIENT

GetTarget(): number

```lua
local target = exports["a_inventory"]:GetTarget()

if not target then
    print("No target selected.")
    return
end

print("Target serverid: " .. target)
```

> Returns the serverid of a selected target.

## FUNCTIONS

# CLIENT

Bind:Create(cmdName: string, key: string, description: string, callback: function): void

```lua
Bind:Create("openinventory", "F1", "Open inventory", function()
    print("F1 pressed.")
end)
```

> Creates a new bind including a "alive" check.

Event:RegisterPrivateEvent(allowedResources: string[], eventName: string, callback: function): void

```lua
local whitelistedResources = { "a_inventory" }

Event:RegisterPrivateEvent(whitelistedResources, "inventory:open", function()
    print("Inventory opened.")
end)
```

> Allows you to register an internal event that only works with the specified resources. (CLIENT > CLIENT ONLY)

GetTarget(): number

```lua
local target = GetTarget()

if not target then
    print("No target selected.")
    return
end

print("Target serverid: " .. target)
```

> Returns the serverid of a selected target. (INTERNAL FUNCTION, ONLY USE WITH IN-SCRIPT PLUGINS)

Inventory:Get(name: string, ...): table, number, boolean, string

```lua
local items, maxWeight, state, error = Inventory:Get("pluginname", "example_argument")

if not state then
  print("Error: " .. error)
  return
end

print("Max weight: " .. maxWeight)
```

> Returns pre-formatted inventory items, max weight and state + errors if any.

Inventory:TriggerHandler(name: string, action: string, ...): void

```lua
Inventory:TriggerHandler("pluginname", "actionname", item, from, to, amount)
```

Inventory:Create(name: string, label: string): table

```lua
  local inventory = Inventory:Create("pluginname", "Example Inventory")
```

> Creates a new inventory, returns inventory object which contains all the functions you need.

### Inventory Object

inventory:Open(items: table, maxWeight: number, secondLabel?: string): void

```lua
local inventory = Inventory:Create("pluginname", "Example Inventory")

local items, maxWeight, state, error = Inventory:Get("pluginname", "example_argument")

inventory:Open(items, maxWeight, "Second Label")
```

inventory:On(name: string, callback: function): void

```lua
local inventory = Inventory:Create("pluginname", "Example Inventory")

inventory:On("moveItem", function(item, from, to, amount)
  print(amount)
end)
```

inventory:MoveItem(item: table, from: table, to: table, amount: number, ...): void

```lua
local inventory = Inventory:Create("pluginname", "Example Inventory")

inventory:On("moveItem", function(item, from, to, amount)
  DebugLog("Move Item", item, from, to, amount, "optional")

  inventory:MoveItem(item, from, to, amount, "optional")
end)
```

inventory:CanAddItem(amount: number, weight: number, maxWeight: number): boolean

```lua
local inventory = Inventory:Create("pluginname", "Example Inventory")

local canAdd = inventory:CanAddItem(1, 1, 10)

if not canAdd then
  print("Can't add item.")
end
```

inventory:Update(items: table): void

```lua
local inventory = Inventory:Create("pluginname", "Example Inventory")

RegisterNetEvent("updateItems", function(items)
  inventory:Update(items)
end)
```

inventory:Close(): void

```lua
local inventory = Inventory:Create("pluginname", "Example Inventory")

inventory:Close()
```

# SHARED

DebugLog(...): void

```lua
DebugLog("Hello", {
  a = "World",
}, 123)
```

> Only logs if the debug mode is set to true.

GenerateUUID(): string

```lua
local uuid = GenerateUUID()

print("Generated UUID: " .. uuid)
```

GetLocale(holder: string, ...): string

```lua
local locale = GetLocale("actions.gave_item", "Name", 1, "Bread")
```

> Returns a localized string, you can use the `...` to pass arguments to the string.

table.includes(table: table, value: any): boolean

```lua
local table = { 1, 2, 3, 4, 5 }

if table.includes(table, 3) then
    print("Table includes 3.")
end
```

# SERVER

IssueLog(logType: string, message: string: action: string): void

````lua
IssueLog("info",
playerName .. GetLocale("actions.used") .. "blehh" .. "\n```" .. "Bla bla" .. "```",
"use")
````

Item:Get(name: string, itemType: "money" | "item" | "weapon"): boolean, table | string

```lua
local success, resp = Item:Get("bread", "item")

if not success then
  print("Error: " .. resp)
  return
end

print("Item: " .. resp.name)
```

> Returns a pre-formatted item object. (Comes with data validation)

Inventory:GetWeight(items: table): number

```lua
local weight = Inventory:GetWeight({
  { count = 1, weight = 1 },
  { count = 1, weight = 1 },
})

print("Total weight: " .. weight)
```

Inventory:Create(name: string): table

```lua
  local inventory = Inventory:Create("pluginname")
```

> Creates a new inventory, returns inventory object which contains all the functions you need.

### Inventory Object

inventory:On(action: string, callback: function): void

```lua
local inventory = Inventory:Create("pluginname")

inventory:On("moveItem", function(item, from, to, amount)
  print(amount)
end)
```
