ESX = exports["es_extended"]:getSharedObject()

-- Lager Status prüfen
RegisterServerEvent('prada_lager:checkLager')
AddEventHandler('prada_lager:checkLager', function()
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if not xPlayer then
        print("ERROR: xPlayer is null for source: " .. tostring(_source))
        return
    end

    local identifier = xPlayer.identifier

    MySQL.Async.fetchAll('SELECT * FROM prada_lager WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    }, function(result)
        if result[1] then
            local items = json.decode(result[1].items)
            TriggerClientEvent('prada_lager:setLagerStatus', _source, true, {
                type = result[1].lager_type,
                size = result[1].lager_size,
                items = items
            })
        else
            TriggerClientEvent('prada_lager:setLagerStatus', _source, false)
        end
    end)
end)

-- Lager kaufen
RegisterServerEvent('prada_lager:buyLager')
AddEventHandler('prada_lager:buyLager', function(lagerType, price, size)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if not xPlayer then
        print("ERROR: xPlayer is null for source: " .. tostring(_source))
        return
    end

    local identifier = xPlayer.identifier
    local playerMoney = xPlayer.getMoney()

    print("Player " .. xPlayer.getName() .. " trying to buy lager. Money: " .. playerMoney .. ", Price: " .. price)

    if playerMoney < price then
        TriggerClientEvent('prada_lager:showNotification', _source, 'Du hast nicht genug Geld!')
        return
    end

    -- Prüfen ob Spieler bereits ein Lager hat
    MySQL.Async.fetchAll('SELECT * FROM prada_lager WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    }, function(result)
        if result[1] then
            TriggerClientEvent('prada_lager:showNotification', _source, 'Du hast bereits ein Lager!')
            return
        end

        -- Geld abziehen
        xPlayer.removeMoney(price)
        print("Money removed from player: " .. price)

        -- Lager in Datenbank eintragen
        MySQL.Async.execute('INSERT INTO prada_lager (identifier, lager_type, lager_size, items) VALUES (@identifier, @lager_type, @lager_size, @items)', {
            ['@identifier'] = identifier,
            ['@lager_type'] = lagerType,
            ['@lager_size'] = size,
            ['@items'] = json.encode({})
        }, function(rowsChanged)
            if rowsChanged > 0 then
                print("Lager successfully created for player: " .. xPlayer.getName())
                TriggerClientEvent('prada_lager:showNotification', _source, 'Lager erfolgreich gekauft!')
                TriggerClientEvent('prada_lager:setLagerStatus', _source, true, {
                    type = lagerType,
                    size = size,
                    items = {}
                })
            else
                print("ERROR: Failed to create lager in database")
                TriggerClientEvent('prada_lager:showNotification', _source, 'Fehler beim Kauf!')
                xPlayer.addMoney(price) -- Geld zurückgeben
            end
        end)
    end)
end)

-- Lager verkaufen
RegisterServerEvent('prada_lager:sellLager')
AddEventHandler('prada_lager:sellLager', function()
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if not xPlayer then
        print("ERROR: xPlayer is null for source: " .. tostring(_source))
        return
    end

    local identifier = xPlayer.identifier

    -- Lager-Daten aus Datenbank holen
    MySQL.Async.fetchAll('SELECT * FROM prada_lager WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    }, function(result)
        if not result[1] then
            TriggerClientEvent('prada_lager:showNotification', _source, 'Du hast kein Lager!')
            return
        end

        local lagerData = result[1]
        local lagerType = lagerData.lager_type
        local lagerSize = lagerData.lager_size

        -- Preis basierend auf Lager-Typ berechnen
        local originalPrice = 0
        if lagerType == 'small' then
            originalPrice = 50000
        elseif lagerType == 'medium' then
            originalPrice = 100000
        elseif lagerType == 'large' then
            originalPrice = 200000
        end

        local sellPrice = math.floor(originalPrice / 2) -- Halber Preis

        -- Lager aus Datenbank löschen
        MySQL.Async.execute('DELETE FROM prada_lager WHERE identifier = @identifier', {
            ['@identifier'] = identifier
        }, function(rowsChanged)
            if rowsChanged > 0 then
                -- Geld geben
                xPlayer.addMoney(sellPrice)

                print("Player " .. xPlayer.getName() .. " sold lager for: " .. sellPrice)

                -- Client benachrichtigen
                TriggerClientEvent('prada_lager:setLagerStatus', _source, false, nil)
                TriggerClientEvent('prada_lager:showNotification', _source, 'Lager für $' .. sellPrice .. ' verkauft!')
            else
                print("ERROR: Failed to delete lager from database")
                TriggerClientEvent('prada_lager:showNotification', _source, 'Fehler beim Verkauf!')
            end
        end)
    end)
end)

-- Item einlagern
RegisterServerEvent('prada_lager:depositItem')
AddEventHandler('prada_lager:depositItem', function(itemName, amount)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if not xPlayer then
        print("ERROR: xPlayer is null for source: " .. tostring(_source))
        return
    end

    local identifier = xPlayer.identifier
    local playerItem = xPlayer.getInventoryItem(itemName)

    if not playerItem or playerItem.count < amount then
        TriggerClientEvent('prada_lager:showNotification', _source, 'Du hast nicht genug Items!')
        return
    end

    MySQL.Async.fetchAll('SELECT * FROM prada_lager WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    }, function(result)
        if not result[1] then
            TriggerClientEvent('prada_lager:showNotification', _source, 'Du hast kein Lager!')
            return
        end

        local lagerData = result[1]
        local items = json.decode(lagerData.items)
        local lagerSize = lagerData.lager_size

        -- Prüfen ob genug Platz im Lager
        local usedSlots = 0
        for itemName2, count in pairs(items) do
            usedSlots = usedSlots + count
        end

        if (usedSlots + amount) > lagerSize then
            TriggerClientEvent('prada_lager:showNotification', _source, 'Nicht genug Platz im Lager!')
            return
        end

        -- Item vom Spieler entfernen
        xPlayer.removeInventoryItem(itemName, amount)

        -- Item zum Lager hinzufügen
        if items[itemName] then
            items[itemName] = items[itemName] + amount
        else
            items[itemName] = amount
        end

        -- Lager in Datenbank aktualisieren
        MySQL.Async.execute('UPDATE prada_lager SET items = @items WHERE identifier = @identifier', {
            ['@identifier'] = identifier,
            ['@items'] = json.encode(items)
        }, function(rowsChanged)
            if rowsChanged > 0 then
                TriggerClientEvent('prada_lager:showNotification', _source, amount .. 'x ' .. playerItem.label .. ' eingelagert!')
            else
                -- Rollback
                xPlayer.addInventoryItem(itemName, amount)
                TriggerClientEvent('prada_lager:showNotification', _source, 'Fehler beim Einlagern!')
            end
        end)
    end)
end)

-- Item auslagern
RegisterServerEvent('prada_lager:withdrawItem')
AddEventHandler('prada_lager:withdrawItem', function(itemName, amount)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if not xPlayer then
        print("ERROR: xPlayer is null for source: " .. tostring(_source))
        return
    end

    local identifier = xPlayer.identifier

    MySQL.Async.fetchAll('SELECT * FROM prada_lager WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    }, function(result)
        if not result[1] then
            TriggerClientEvent('prada_lager:showNotification', _source, 'Du hast kein Lager!')
            return
        end

        local lagerData = result[1]
        local items = json.decode(lagerData.items)

        if not items[itemName] or items[itemName] < amount then
            TriggerClientEvent('prada_lager:showNotification', _source, 'Nicht genug Items im Lager!')
            return
        end

        -- Prüfen ob Spieler Platz im Inventar hat
        local playerItem = xPlayer.getInventoryItem(itemName)
        if not xPlayer.canCarryItem(itemName, amount) then
            TriggerClientEvent('prada_lager:showNotification', _source, 'Nicht genug Platz im Inventar!')
            return
        end

        -- Item aus Lager entfernen
        items[itemName] = items[itemName] - amount
        if items[itemName] <= 0 then
            items[itemName] = nil
        end

        -- Item zum Spieler hinzufügen
        xPlayer.addInventoryItem(itemName, amount)

        -- Lager in Datenbank aktualisieren
        MySQL.Async.execute('UPDATE prada_lager SET items = @items WHERE identifier = @identifier', {
            ['@identifier'] = identifier,
            ['@items'] = json.encode(items)
        }, function(rowsChanged)
            if rowsChanged > 0 then
                local itemLabel = ESX.GetItemLabel(itemName)
                TriggerClientEvent('prada_lager:showNotification', _source, amount .. 'x ' .. itemLabel .. ' ausgelagert!')
            else
                -- Rollback
                xPlayer.removeInventoryItem(itemName, amount)
                TriggerClientEvent('prada_lager:showNotification', _source, 'Fehler beim Auslagern!')
            end
        end)
    end)
end)

-- ESX Callbacks
ESX.RegisterServerCallback('prada_lager:getPlayerInventory', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        print("ERROR: xPlayer is null in getPlayerInventory callback for source: " .. tostring(source))
        cb({})
        return
    end

    cb(xPlayer.inventory)
end)

ESX.RegisterServerCallback('prada_lager:getLagerInventory', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        print("ERROR: xPlayer is null in getLagerInventory callback for source: " .. tostring(source))
        cb({})
        return
    end

    local identifier = xPlayer.identifier

    MySQL.Async.fetchAll('SELECT * FROM prada_lager WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    }, function(result)
        if result[1] then
            local items = json.decode(result[1].items)
            cb(items)
        else
            cb({})
        end
    end)
end)

ESX.RegisterServerCallback('prada_lager:getLagerInfo', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        print("ERROR: xPlayer is null in getLagerInfo callback for source: " .. tostring(source))
        cb(nil)
        return
    end

    local identifier = xPlayer.identifier

    MySQL.Async.fetchAll('SELECT * FROM prada_lager WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    }, function(result)
        if result[1] then
            local items = json.decode(result[1].items)
            cb({
                type = result[1].lager_type,
                size = result[1].lager_size,
                items = items
            })
        else
            cb(nil)
        end
    end)
end)

ESX.RegisterServerCallback('ESX:GetItemLabel', function(source, cb, itemName)
    cb(ESX.GetItemLabel(itemName))
end)

-- Callback für Lager-Items (für A-Inventory)
ESX.RegisterServerCallback('prada_lager:getLagerItems', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        cb(nil, nil)
        return
    end

    local identifier = xPlayer.identifier

    MySQL.Async.fetchAll('SELECT * FROM prada_lager WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    }, function(result)
        if result[1] then
            local lagerData = result[1]
            local items = json.decode(lagerData.items)
            local maxWeight = lagerData.lager_size * 1000

            -- Items in A-Inventory Format konvertieren
            local formattedItems = {}
            for itemName, count in pairs(items) do
                if count > 0 then
                    table.insert(formattedItems, {
                        name = itemName,
                        count = count,
                        slot = #formattedItems + 1
                    })
                end
            end

            cb(formattedItems, maxWeight)
        else
            cb(nil, nil)
        end
    end)
end)

-- Inventar-System Integration
RegisterServerEvent('prada_lager:registerStash')
AddEventHandler('prada_lager:registerStash', function()
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if not xPlayer then return end

    local identifier = xPlayer.identifier
    local stashId = 'prada_lager_' .. identifier

    -- Lager-Daten aus Datenbank holen
    MySQL.Async.fetchAll('SELECT * FROM prada_lager WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    }, function(result)
        if result[1] then
            local lagerData = result[1]
            local items = json.decode(lagerData.items)
            local lagerSize = lagerData.lager_size

            print("Registering stash for player: " .. xPlayer.getName() .. " with size: " .. lagerSize)

            -- A-Inventory Stash registrieren (Priorität 1)
            if GetResourceState('a_inventory') == 'started' then
                exports['a_inventory']:RegisterStash(stashId, 'Prada Lager', lagerSize, lagerSize * 1000)
                print("A-Inventory stash registered: " .. stashId)

                -- Items in Stash laden
                for itemName, count in pairs(items) do
                    if count > 0 then
                        exports['a_inventory']:AddItem(stashId, itemName, count)
                        print("Added item to A-Inventory stash: " .. itemName .. " x" .. count)
                    end
                end
            end

            -- OX-Inventory Stash registrieren (Priorität 2)
            if GetResourceState('ox_inventory') == 'started' then
                exports['ox_inventory']:RegisterStash(stashId, 'Prada Lager', lagerSize, lagerSize * 1000)
                print("OX-Inventory stash registered: " .. stashId)

                -- Items in Stash laden
                for itemName, count in pairs(items) do
                    if count > 0 then
                        exports['ox_inventory']:AddItem(stashId, itemName, count)
                        print("Added item to stash: " .. itemName .. " x" .. count)
                    end
                end
            end

            -- QS-Inventory Stash registrieren
            if GetResourceState('qs-inventory') == 'started' then
                exports['qs-inventory']:RegisterStash(stashId, lagerSize, lagerSize * 1000)
                print("QS-Inventory stash registered: " .. stashId)
            end

            -- ESX InventoryHUD
            if GetResourceState('esx_inventoryhud') == 'started' then
                -- InventoryHUD verwendet automatische Registrierung über Datenbank
                print("ESX InventoryHUD detected")
            end
        end
    end)
end)

-- Stash-Daten synchronisieren für verschiedene Inventar-Systeme

-- A-Inventory (Priorität 1)
if GetResourceState('a_inventory') == 'started' then
    AddEventHandler('a_inventory:stashUpdated', function(stashId, items)
        if string.find(stashId, 'prada_lager_') then
            local identifier = string.gsub(stashId, 'prada_lager_', '')

            -- Items in Datenbank speichern
            local itemsData = {}
            for slot, item in pairs(items) do
                if item.name and item.count > 0 then
                    itemsData[item.name] = (itemsData[item.name] or 0) + item.count
                end
            end

            MySQL.Async.execute('UPDATE prada_lager SET items = @items WHERE identifier = @identifier', {
                ['@identifier'] = identifier,
                ['@items'] = json.encode(itemsData)
            })
            print("A-Inventory: Stash saved for " .. identifier)
        end
    end)
end

-- OX-Inventory
if GetResourceState('ox_inventory') == 'started' then
    AddEventHandler('ox_inventory:stashSaved', function(stashId, items)
        if string.find(stashId, 'prada_lager_') then
            local identifier = string.gsub(stashId, 'prada_lager_', '')

            -- Items in Datenbank speichern
            local itemsData = {}
            for slot, item in pairs(items) do
                if item.name and item.count > 0 then
                    itemsData[item.name] = (itemsData[item.name] or 0) + item.count
                end
            end

            MySQL.Async.execute('UPDATE prada_lager SET items = @items WHERE identifier = @identifier', {
                ['@identifier'] = identifier,
                ['@items'] = json.encode(itemsData)
            })
            print("OX-Inventory: Stash saved for " .. identifier)
        end
    end)
end

-- QS-Inventory
if GetResourceState('qs-inventory') == 'started' then
    AddEventHandler('qs-inventory:server:SaveStashItems', function(stashId, items)
        if string.find(stashId, 'prada_lager_') then
            local identifier = string.gsub(stashId, 'prada_lager_', '')

            local itemsData = {}
            for slot, item in pairs(items) do
                if item.name and item.amount > 0 then
                    itemsData[item.name] = (itemsData[item.name] or 0) + item.amount
                end
            end

            MySQL.Async.execute('UPDATE prada_lager SET items = @items WHERE identifier = @identifier', {
                ['@identifier'] = identifier,
                ['@items'] = json.encode(itemsData)
            })
            print("QS-Inventory: Stash saved for " .. identifier)
        end
    end)
end

-- ESX InventoryHUD
if GetResourceState('esx_inventoryhud') == 'started' then
    AddEventHandler('esx_inventoryhud:onStashUpdate', function(stashId, items)
        if string.find(stashId, 'prada_lager_') then
            local identifier = string.gsub(stashId, 'prada_lager_', '')

            MySQL.Async.execute('UPDATE prada_lager SET items = @items WHERE identifier = @identifier', {
                ['@identifier'] = identifier,
                ['@items'] = json.encode(items)
            })
            print("ESX InventoryHUD: Stash saved for " .. identifier)
        end
    end)
end