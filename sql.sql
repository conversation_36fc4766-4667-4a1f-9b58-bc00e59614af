CREATE TABLE IF NOT EXISTS `prada_lager` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(60) NOT NULL,
  `lager_type` varchar(50) NOT NULL,
  `lager_size` int(11) NOT NULL,
  `items` longtext DEFAULT '{}',
  `price` int(11) DEFAULT 0,
  `purchased_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier` (`identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Falls die Tabelle bereits existiert, füge die price Spalte hinzu
ALTER TABLE `prada_lager` ADD COLUMN IF NOT EXISTS `price` int(11) DEFAULT 0;