local glovebox = Inventory:Create('glovebox', GetLocale("plugins.glovebox.name"))

local currentVehicle = {
  vehicle = nil,
  model = nil,
  plate = nil,
  maxWeight = nil
}

local allowedInventories = {
  "player",
  "glovebox"
}

EventHandler:On("ToggleInventory", function(visible)
  local vehicle = GetVehiclePedIsIn(PED, false)
  if not vehicle or vehicle == 0 or not visible then
    return
  end

  local plate = GetVehicleNumberPlateText(vehicle)
  local class = GetVehicleClass(vehicle)
  local model = GetDisplayNameFromVehicleModel(GetEntityModel(vehicle))

  local items, maxWeight, state, error = Inventory:Get("glovebox", plate, class, model)
 
  if not state then
    return ClientNotify("info", GetLocale("plugins.glovebox.name"), error)
  end

  currentVehicle.vehicle = vehicle
  currentVehicle.model = model
  currentVehicle.plate = plate
  currentVehicle.maxWeight = maxWeight

  glovebox:Open(items, maxWeight, plate)
end)

glovebox:On("moveItem", function(item, from, to, amount)
  if not currentVehicle.plate or not currentVehicle.maxWeight then
    return ClientNotify("info", GetLocale("plugins.glovebox.name"), GetLocale("plugins.glovebox.vehicle_not_found"))
  end

  local targetMaxWeight = to.type == "player" and ESX.PlayerData?.maxWeight or currentVehicle.maxWeight

  if from.type == "player" then
    local itemData = Items:GetItem(item.name)

    if not itemData then
      return ClientNotify("info", GetLocale("plugins.glovebox.name"), GetLocale("plugins.glovebox.item_doesnt_exist"))
    end

    if not glovebox:CanAddItem(amount, itemData.weight, targetMaxWeight) then
      return ClientNotify("info", GetLocale("plugins.glovebox.name"), GetLocale("plugins.glovebox.item_too_heavy"))
    end
  end

  if not table.includes(allowedInventories, from.type) then
    return ClientNotify("info", GetLocale("plugins.glovebox.name"), GetLocale("plugins.glovebox.item_doesnt_exist"))
  end

  if not table.includes(allowedInventories, to.type) then
    return ClientNotify("info", GetLocale("plugins.glovebox.name"), GetLocale("plugins.glovebox.item_doesnt_exist"))
  end

  local vehicleClass = GetVehicleClass(currentVehicle.vehicle)

  glovebox:MoveItem(item, from, to, amount, currentVehicle.plate, vehicleClass, currentVehicle.model)
end)

RegisterNetEvent("a_inventory:updateGlovebox", function(items)
  if not currentVehicle.plate then
    return
  end

  items = json.decode(items)

  glovebox:Update(items)
end)

EventHandler:On("NuiFocus", function(display)
  if not display and currentVehicle?.plate then
    TriggerServerEvent("a_inventory:closeGlovebox", currentVehicle.plate)

    currentVehicle = {
      vehicle = nil,
      model = nil,
      plate = nil,
      maxWeight = nil
    }
  end
end)