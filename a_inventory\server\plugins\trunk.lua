local trunks, openTrunks = {}, {}
local trunk = Inventory:Create('trunk')
local isInstalled = pcall(MySQL.query.await, "SELECT * FROM trunks")
local allowedInventories = {
  "player",
  "trunk"
}

if not isInstalled then
  print("Trunk plugin installation not found. Installing...")

  MySQL.Sync.execute([[
    CREATE TABLE IF NOT EXISTS trunks (
      plate VARCHAR(255) PRIMARY KEY,
      items TEXT
    )
  ]])

  print("Trunk plugin installation complete. Please restart the resource to apply changes.")
end

local function broadcastTrunkUpdate(plate)
  local players = openTrunks[plate]

  if players then
    for i = 1, #players do
      TriggerClientEvent("a_inventory:updateTrunk", players[i], trunks[plate].items)
    end
  end
end

---@param plate string
---@param itemData table
---@param amount number
---@param maxSpace number
---@param components? table
---@param tint? number
---@param ammo? number
---@return boolean, string
local function AddToTrunk(plate, itemData, amount, maxSpace, components, tint, ammo)
  local trunkItems = trunks[plate]

  if not trunkItems then
    return false, GetLocale("plugins.trunk.vehicle_doenst_have_trunk")
  end

  local items = json.decode(trunkItems.items)

  if not items then
    return false, GetLocale("plugins.trunk.error_occured", "E-EF-1")
  end

  local totalWeight = 0

  for i = 1, #items do
    totalWeight += ((items[i]?.weight or 0) * items[i].count)
  end

  if totalWeight + ((itemData?.weight or 0) * amount) > maxSpace then
    return false, GetLocale("plugins.trunk.trunk_too_small")
  end

  for i = 1, #items do
    if items[i].name == itemData.name and itemData.type ~= "weapon" then
      items[i].count += amount
      trunkItems.items = json.encode(items)

      MySQL.Async.execute("UPDATE trunks SET items = @items WHERE plate = @plate", {
        ['@items'] = trunkItems.items,
        ['@plate'] = plate
      })

      trunks[plate] = trunkItems

      return true, ""
    elseif items[i].name == itemData.name and itemData.type == "weapon" then
      return false, GetLocale("plugins.trunk.only_one_weapon")
    end
  end

  items[#items + 1] = {
    name = itemData.name,
    count = amount,
    weight = itemData.weight,
    type = itemData.type,
    label = itemData.label,
    usable = itemData.usable,
    canRemove = itemData.canRemove,
  }

  if components then
    items[#items].components = components
  end

  if tint then
    items[#items].tint = tint
  end

  if ammo then
    items[#items].ammo = ammo
  end

  trunkItems.items = json.encode(items)

  MySQL.Async.execute("UPDATE trunks SET items = @items WHERE plate = @plate", {
    ['@items'] = trunkItems.items,
    ['@plate'] = plate
  })

  trunks[plate] = trunkItems

  IssueLog("info", ("Player %s added %s x%s to %s's trunk"):format(source, itemData.label, amount, plate), "trunk")
  return true, ""
end

---@param plate string
---@param itemData table
---@param amount number
---@return boolean, string
local function RemoveFromTrunk(plate, itemData, amount)
  local trunkItems = trunks[plate]

  if not trunkItems then
    return false, GetLocale("plugins.trunk.vehicle_doenst_have_trunk")
  end

  local items = json.decode(trunkItems.items)

  if not items then
    return false, GetLocale("plugins.trunk.error_occured", "E-EF-2")
  end

  for i = 1, #items do
    if items[i].name == itemData.name then
      if items[i].count < amount then
        return false, GetLocale("plugins.trunk.not_enough_item")
      end

      items[i].count -= amount

      if items[i].count == 0 then
        table.remove(items, i)
      end

      trunkItems.items = json.encode(items)

      MySQL.Async.execute("UPDATE trunks SET items = @items WHERE plate = @plate", {
        ['@items'] = trunkItems.items,
        ['@plate'] = plate
      })

      trunks[plate] = trunkItems

      IssueLog("info", ("Player %s removed %s x%s from %s's trunk"):format(source, itemData.label, amount, plate), "trunk")
      return true, ""
    end
  end

  return false, GetLocale("plugins.trunk.item_doesnt_exist_in_trunk")  
end

trunk:On("getInventory", function(source, cb, plate, vehicleClass, vehicleModel)
  local trunkItems = MySQL.query.await("SELECT * FROM trunks WHERE plate = @plate", {
    ['@plate'] = plate
  })?[1]

  local maxWeight = Config.BlacklistedVehicleTypesGB[vehicleClass] and nil or
      Config.VehicleModel[plate] or
      Config.VehicleModel[string.lower(vehicleModel)] or
      Config.TrunkWeights[vehicleClass] or Config.DefaultWeight

  if maxWeight == nil then
    return cb({
      state = false,
      error = GetLocale("plugins.trunk.vehicle_doenst_have_trunk"),
      items = {},
      maxWeight = 0
    })
  end

  if not trunkItems then
    local doesExist = MySQL.query.await("SELECT * FROM owned_vehicles WHERE plate = @plate", {
      ['@plate'] = plate
    })?[1]

    if not doesExist then
      return cb({
        state = false,
        error = GetLocale("plugins.trunk.vehicle_doesnt_exist"),
        items = {},
        maxWeight = 0
      })
    end

    MySQL.Async.execute("INSERT INTO trunks (plate, items) VALUES (@plate, @items)", {
      ['@plate'] = plate,
      ['@items'] = json.encode({})
    })

    trunkItems = {
      items = json.encode({}),
    }
  end

  trunks[plate] = trunkItems

  cb({
    state = true,
    error = "",
    items = json.decode(trunkItems?.items or {}),
    maxWeight = maxWeight
  })

  if not openTrunks[plate] then
    openTrunks[plate] = {}
  end

  openTrunks[plate][#openTrunks[plate] + 1] = source
end)

trunk:On("moveItem", function(source, cb, item, from, to, amount, plate, vehicleType, vehicleModel)
  if not plate then
    return cb({
      state = false,
      error = GetLocale("plugins.trunk.vehicle_doesnt_exist"),
    })
  end

  if type(amount) == "number" then
    amount = math.floor(amount)
  end

  if not table.includes(allowedInventories, from?.type or "") or not table.includes(allowedInventories, to?.type or "") then
    return cb({
      state = false,
      error = GetLocale("plugins.trunk.invalid_inventory"),
    })
  end

  local maxWeight = Config.BlacklistedVehicleTypesGB[vehicleType] and nil or
      Config.VehicleModel[plate] or
      Config.VehicleModel[string.lower(vehicleModel)] or
      Config.TrunkWeights[vehicleType] or Config.DefaultWeight

  local trunkItems = trunks[plate]

  if not trunkItems then
    return cb({
      state = false,
      error = GetLocale("plugins.trunk.vehicle_doenst_have_trunk"),
    })
  end

  local isOpen = table.includes(openTrunks[plate], source)

  if not isOpen then
    return cb({
      state = false,
      error = GetLocale("plugins.trunk.not_open"),
    })
  end

  local xPlayer = ESX.GetPlayerFromId(source)

  if not xPlayer then
    return cb({
      state = false,
      error = GetLocale("plugins.trunk.error_occured", "E-1"),
    })
  end

  if to.type == "trunk" then
    local items = json.decode(trunkItems.items)

    if not items then
      return cb({
        state = false,
        error = GetLocale("plugins.trunk.error_occured", "E-2"),
      })
    end
  elseif to.type == "player" then
    if item.type == "item" then
      local canCarry = xPlayer.canCarryItem(item.name, amount)

      if not canCarry then
        return cb({
          state = false,
          error = GetLocale("plugins.trunk.cannot_carry_item"),
        })
      end
    end
  end

  local success, resp = Item:Get(item.name, item.type)

  if not success then
    return cb({
      state = false,
      error = resp,
    })
  end

  local itemCache = {}

  if from.type == "player" then
    if item.type == "money" then
      local account = xPlayer.getAccount(item.name)

      if not account then
        return cb({
          state = false,
          error = GetLocale("item_validation.account_does_not_exist"),
        })
      end

      if account.money < amount then
        return cb({
          state = false,
          error = GetLocale("plugins.trunk.not_enough_money"),
        })
      end

      xPlayer.removeAccountMoney(item.name, amount)
    elseif item.type == "weapon" then
      if not xPlayer.hasWeapon(item.name) then
        return cb({
          state = false,
          error = GetLocale("plugins.trunk.dont_have_weapon"),
        })
      end

      local loadout = xPlayer.getLoadout(true)
      local tint = xPlayer.getWeaponTint(string.upper(item.name))
      local weaponData = loadout[string.upper(item.name)]

      itemCache = {
        ammo = weaponData.ammo,
        components = weaponData.components,
        tint = tint,
      }

      xPlayer.removeWeapon(item.name)
    elseif item.type == "item" then
      local inventoryItem = xPlayer.getInventoryItem(item.name)

      if not inventoryItem then
        return cb({
          state = false,
          error = GetLocale("item_validation.item_does_not_exist"),
        })
      end

      if inventoryItem.count < amount then
        return cb({
          state = false,
          error = GetLocale("plugins.trunk.not_enough_item"),
        })
      end

      xPlayer.removeInventoryItem(item.name, amount)
    end

    local success, error = AddToTrunk(plate, type(resp) == "table" and resp or {}, amount, maxWeight, itemCache.components, itemCache.tint, itemCache.ammo)

    if not success then
      if item.type == "money" then
        xPlayer.addAccountMoney(item.name, amount)
      elseif item.type == "weapon" then
        xPlayer.addWeapon(item.name, itemCache.ammo)

        if itemCache.components then
          for i = 1, #itemCache.components do
            ---@diagnostic disable-next-line: param-type-mismatch
            xPlayer.addWeaponComponent(item.name, itemCache.components[i])
          end
        end

        if itemCache.tint then
          xPlayer.setWeaponTint(item.name, itemCache.tint)
        end
      elseif item.type == "item" then
        xPlayer.addInventoryItem(item.name, amount)
      end

      return cb({
        state = false,
        error = error,
      })
    end

    cb({
      state = true,
      error = "",
    })

    broadcastTrunkUpdate(plate)
  elseif from.type == "trunk" then
    local success, error = RemoveFromTrunk(plate, resp, amount)

    if not success then
      return cb({
        state = false,
        error = error,
      })
    end

    if to.type == "player" then
      if item.type == "money" then
        xPlayer.addAccountMoney(item.name, amount)
      elseif item.type == "weapon" then
        xPlayer.addWeapon(item.name, item.ammo)

        if item.components then
          for i = 1, #item.components do
            xPlayer.addWeaponComponent(item.name, item.components[i])
          end
        end

        if item.tint then
          xPlayer.setWeaponTint(item.name, item.tint)
        end
      elseif item.type == "item" then
        if not xPlayer.canCarryItem(item.name, amount) then
          return cb({
            state = false,
            error = GetLocale("plugins.trunk.cannot_carry_item"),
          })
        end
        
        xPlayer.addInventoryItem(item.name, amount)
      end

      cb({
        state = true,
        error = "",
      })

      broadcastTrunkUpdate(plate)
    elseif to.type == "trunk" then
      local success, error = AddToTrunk(plate, resp, amount, maxWeight)

      if not success then
        return cb({
          state = false,
          error = error,
        })
      end

      cb({
        state = true,
        error = "",
      })

      broadcastTrunkUpdate(plate)
      -- tweaky ig
    end
  end
end)

RegisterNetEvent("a_inventory:closeTrunk", function(plate)
  local source = source

  if not plate then
    return
  end

  local players = openTrunks[plate]

  if not players then
    return
  end

  for i = 1, #players do
    if players[i] == source then
      table.remove(players, i)

      DebugLog(("Removed Player %s from %s's broadcast list"):format(source, plate))
      break
    end
  end
end)