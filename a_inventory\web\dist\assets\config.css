:root {
  --clr-white: 255 255 255;
  --clr-black: 0 0 0;

  --clr-weight-progress: linear-gradient(90deg,rgb(153, 0, 255) 0%, #8400ff 100%);

  --clr-actions-info-btn: 0 144 255;
  --clr-actions-use-btn: 108 72 46;
  --clr-actions-use-btn-glow: rgb(180, 101, 40);
  --clr-actions-give-btn: 230 255 0;
  --clr-actions-split-btn: 140 255 0;

  --clr-header-primary: #8c00ff;

  --clr-item-hover: 145 0 255;

  --clr-give-ui-bg: linear-gradient(
    200deg,
    rgba(105, 55, 162, 0.33) 0%,
    rgba(35, 18, 74, 0.75) 20.28%,
    rgba(26, 9, 46, 0.75) 39.66%,
    rgba(19, 8, 33, 0.75) 60.29%,
    rgba(31, 9, 45, 0.75) 79.95%,
    rgba(84, 12, 112, 0.75) 100%
  );
   --clr-give-ui-btn-primary: 105 0 144;
  --clr-give-ui-btn-primary-glow: 154 0 255;
  --clr-give-ui-btn-secondary: 182 0 0;
  --clr-give-ui-btn-secondary-glow: 255 0 0;

  --clr-notification-success-bg: #1f8e00;
  --clr-notification-success-shadow: #51ff00;
  --clr-notification-success-icon: #9dff57;
  --clr-notification-error-bg: rgba(182, 0, 0, 0.45);
  --clr-notification-error-shadow: rgba(255, 0, 0, 0.75);
  --clr-notification-error-icon: #ff3f3f;
  --clr-notification-info-bg: rgba(130, 0, 182, 0.45);
  --clr-notification-info-shadow: rgba(183, 0, 255, 0.75);
  --clr-notification-info-icon: #a23fff;

  --clr-scrollbar-thumb: #9900ff;
}
